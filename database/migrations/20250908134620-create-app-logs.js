"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.createTable("app_logs", {
        log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false,
        },
        platform: {
          type: Sequelize.ENUM("APP", "CMS"),
          allowNull: false,
        },
        performed_by_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
        performed_by_role: {
          type: Sequelize.ENUM('Admin', 'Company', 'User'),
          allowNull: true,
        },
        performed_by_name: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        action_performed_on_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
        action_performed_on_name: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        action_type: {
          type: Sequelize.ENUM(
            "LOGIN",
            "LOGOUT",

            "STAGE_ASSIGN",
            "STAGE_REMOVE",

            "ITEM_CREATE",
            "ITEM_UPDATE",
            "ITEM_DELETE",

            "ITEM_ASSIGN_TO_STORAGE",
            "ITEM_REMOVE_FROM_STORAGE",
            "ITEM_REMOVE_FROM_INVENTORY",

            "UNIT_MAP",
            "UNIT_MOVE",

            "SHIPMENT_CREATE",
            "SHIPMENT_UPDATE",
            "SHIPMENT_DELETE",

            "ITEM_SUGGESTION_CREATE",
            "ITEM_SUGGESTION_UPDATE",
            "ITEM_SUGGESTION_DELETE",
            "ITEM_SUGGESTION_ACTIVATE",
            "ITEM_SUGGESTION_DEACTIVATE",

            "ROOM_CREATE",
            "ROOM_UPDATE",
            "ROOM_DELETE",
            "ROOM_ACTIVATE",
            "ROOM_DEACTIVATE",

            "SHIPMENT_TYPE_CREATE",
            "SHIPMENT_TYPE_UPDATE",
            "SHIPMENT_TYPE_DELETE",
            "SHIPMENT_TYPE_ACTIVATE",
            "SHIPMENT_TYPE_DEACTIVATE",

            "SHIPMENT_TYPE_STAGE_UPDATE",
            "SHIPMENT_TYPE_STAGE_ACTIVATE",
            "SHIPMENT_TYPE_STAGE_DEACTIVATE",
  
            "SHIPMENT_TYPE_STAGE_OF_SHIPMENT_CREATE",
            "SHIPMENT_TYPE_STAGE_OF_SHIPMENT_UPDATE",
            "SHIPMENT_TYPE_STAGE_OF_SHIPMENT_DELETE",

            "TAG_CREATE",
            "TAG_UPDATE",
            "TAG_DELETE",

            "USER_CREATE",
            "USER_UPDATE",
            "USER_DELETE",
            "USER_ACTIVATE",
            "USER_DEACTIVATE",

            "CUSTOMER_CREATE",
            "CUSTOMER_UPDATE",
            "CUSTOMER_DELETE",
            "CUSTOMER_ACTIVATE",
            "CUSTOMER_DEACTIVATE",

            "VIEW_ITEMS",
            "SCAN_QR",
            "MANUAL_SELECT"
          ),
          allowNull: false,
        },
        affected_fields: {
          type: Sequelize.JSON,
          allowNull: true,
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        }
      });
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable("app_logs");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
