const { includes } = require("lodash");
const {
	app_logs,
	staff,
	admin,
	customer,
	company,
	shipment_job,
	shipment_job_assign_worker_list,
	shipment_room
} = require("../../database/schemas");

exports.fetchActionLog = async () => {
	return app_logs.findAndCountAll({
		order: [['created_at', 'DESC']]
	});
}

exports.createActionLog = async (request) => {
	return app_logs.create(request);
}


/**
 * Helper function to capture affected fields for CREATE operations
 * @param {Object} requestBody - The request body containing the fields
 * @param {Array} excludeFields - Fields to exclude from tracking (optional)
 * @returns {Object} Object containing key-value pairs of affected fields
 */
exports.captureAffectedFields = (requestBody, excludeFields = []) => {
	if (!requestBody || typeof requestBody !== 'object') {
		return {};
	}

	// Default fields to exclude from tracking
	const defaultExcludeFields = [
		'password',
		'confirm_password',
		'access_token',
		'refresh_token',
		'created_at',
		'updated_at',
		'is_deleted',
		"admin_id",
		"company_id",
		"staff_id",
		"customer_id"
	];

	const fieldsToExclude = [...defaultExcludeFields, ...excludeFields];

	// Create object with key-value pairs for affected fields
	const affectedFields = {};

	Object.keys(requestBody).forEach(field => {
		// Include field if it's not excluded and has a valid value
		if (!fieldsToExclude.includes(field) &&
			requestBody[field] !== undefined &&
			requestBody[field] !== null &&
			requestBody[field] !== '') {
			affectedFields[field] = requestBody[field];
		}
	});

	return affectedFields;
}

/**
 * Helper function to map request field names to database field names
 * @param {string} requestField - Field name from request body
 * @returns {string} Corresponding database field name
 */
const mapRequestFieldToDbField = (requestField) => {
	const fieldMapping = {
		// Item suggestion fields
		'item_name': 'name',
		'item_volume': 'volume',
		'item_weight': 'weight',
		'item_description': 'description',
		'item_status': 'status',

		// Tag fields
		'tag_name': 'name',
		'tag_for': 'tag_for',
		'tag_color': 'color',

		// Room fields
		'room_name': 'name',
		'room_status': 'status',

		// User/Staff fields
		'user_first_name': 'first_name',
		'user_last_name': 'last_name',
		'user_email': 'email',
		'user_phone': 'phone',
		'user_country_code': 'country_code',
		'user_roles': 'roles',
		'user_notes': 'notes',
		'user_status': 'status',

		// Customer fields
		'customer_first_name': 'first_name',
		'customer_last_name': 'last_name',
		'customer_email': 'email',
		'customer_phone': 'phone',
		'customer_country_code': 'country_code',
		'customer_notes': 'notes',
		'customer_status': 'status'
	};

	return fieldMapping[requestField] || requestField;
}

/**
 * Helper function to capture both old and new values for UPDATE operations
 * @param {Object} oldData - The existing data before update
 * @param {Object} newData - The new data from request body
 * @param {Array} excludeFields - Fields to exclude from tracking (optional)
 * @returns {Object} Object containing old_values and new_values
 */
exports.captureUpdateFields = (oldData, newData, excludeFields = []) => {
	if (!newData || typeof newData !== 'object') {
		return {};
	}

	// Default fields to exclude from tracking
	const defaultExcludeFields = [
		'password',
		'confirm_password',
		'access_token',
		'refresh_token',
		'created_at',
		'updated_at',
		'is_deleted',
		"admin_id",
		"company_id",
		"staff_id",
		"customer_id"
	];

	const fieldsToExclude = [...defaultExcludeFields, ...excludeFields];

	const oldValues = {};
	const newValues = {};

	Object.keys(newData).forEach(field => {
		// Include field if it's not excluded and has a valid value
		if (!fieldsToExclude.includes(field) &&
			newData[field] !== undefined &&
			newData[field] !== null &&
			newData[field] !== '') {

			// Map request field to database field
			const dbField = mapRequestFieldToDbField(field);

			// Store old value using the mapped database field name
			oldValues[field] = oldData && oldData[dbField] !== undefined ? oldData[dbField] : null;

			// Store new value using the request field name
			newValues[field] = newData[field];
		}
	});

	return {
		old_values: oldValues,
		new_values: newValues
	};
}