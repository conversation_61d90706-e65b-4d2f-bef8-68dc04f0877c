const { includes } = require("lodash");
const {
	app_logs,
	staff,
	admin,
	customer,
	company,
	shipment_job,
	shipment_job_assign_worker_list,
	shipment_room
} = require("../../database/schemas");

exports.fetchActionLog = async () => {
	return app_logs.findAndCountAll({
		order: [['created_at', 'DESC']]
	});
}

exports.createActionLog = async (request) => {
	return app_logs.create(request);
}


exports.captureAffectedFields = (requestBody, excludeFields = []) => {
	if (!requestBody || typeof requestBody !== 'object') {
		return {};
	}

	// Default fields to exclude from tracking
	const defaultExcludeFields = [
		'password',
		'confirm_password',
		'access_token',
		'refresh_token',
		'created_at',
		'updated_at',
		'is_deleted'
	];

	const fieldsToExclude = [...defaultExcludeFields, ...excludeFields];

	// Create object with key-value pairs for affected fields
	const affectedFields = {};

	Object.keys(requestBody).forEach(field => {
		// Include field if it's not excluded and has a valid value
		if (!fieldsToExclude.includes(field) &&
			requestBody[field] !== undefined &&
			requestBody[field] !== null &&
			requestBody[field] !== '') {
			affectedFields[field] = requestBody[field];
		}
	});

	return affectedFields;
}