const { includes } = require("lodash");
const {
	app_logs,
	staff,
	admin,
	customer,
	company,
	shipment_job,
	shipment_job_assign_worker_list,
	shipment_room
} = require("../../database/schemas");

exports.fetchActionLog = async () => {
	return app_logs.findAndCountAll({
		order: [['created_at', 'DESC']]
	});
}

exports.createActionLog = async (request) => {
	return app_logs.create(request);
}


/**
 * Helper function to capture affected fields for CREATE operations
 * @param {Object} requestBody - The request body containing the fields
 * @param {Array} excludeFields - Fields to exclude from tracking (optional)
 * @returns {Object} Object containing key-value pairs of affected fields
 */
exports.captureAffectedFields = (requestBody, excludeFields = []) => {
	if (!requestBody || typeof requestBody !== 'object') {
		return {};
	}

	// Default fields to exclude from tracking
	const defaultExcludeFields = [
		'password',
		'confirm_password',
		'access_token',
		'refresh_token',
		'created_at',
		'updated_at',
		'is_deleted'
	];

	const fieldsToExclude = [...defaultExcludeFields, ...excludeFields];

	// Create object with key-value pairs for affected fields
	const affectedFields = {};

	Object.keys(requestBody).forEach(field => {
		// Include field if it's not excluded and has a valid value
		if (!fieldsToExclude.includes(field) &&
			requestBody[field] !== undefined &&
			requestBody[field] !== null &&
			requestBody[field] !== '') {
			affectedFields[field] = requestBody[field];
		}
	});

	return affectedFields;
}

/**
 * Helper function to capture both old and new values for UPDATE operations
 * @param {Object} oldData - The existing data before update
 * @param {Object} newData - The new data from request body
 * @param {Array} excludeFields - Fields to exclude from tracking (optional)
 * @returns {Object} Object containing old_values and new_values
 */
exports.captureUpdateFields = (oldData, newData, excludeFields = []) => {
	if (!newData || typeof newData !== 'object') {
		return {};
	}

	// Default fields to exclude from tracking
	const defaultExcludeFields = [
		'password',
		'confirm_password',
		'access_token',
		'refresh_token',
		'created_at',
		'updated_at',
		'is_deleted',
		"admin_id",
		"company_id",
		"staff_id",
		"customer_id"
	];

	const fieldsToExclude = [...defaultExcludeFields, ...excludeFields];

	const oldValues = {};
	const newValues = {};

	Object.keys(newData).forEach(field => {
		// Include field if it's not excluded and has a valid value
		if (!fieldsToExclude.includes(field) &&
			newData[field] !== undefined &&
			newData[field] !== null &&
			newData[field] !== '') {

			// Store old value (if exists)
			oldValues[field] = oldData && oldData[field] !== undefined ? oldData[field] : null;

			// Store new value
			newValues[field] = newData[field];
		}
	});

	return {
		old_values: oldValues,
		new_values: newValues
	};
}