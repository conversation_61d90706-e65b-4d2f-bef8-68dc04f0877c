const { includes } = require("lodash");
const {
	app_logs,
	staff,
	admin,
	customer,
	company,
	shipment_job,
	shipment_job_assign_worker_list,
	shipment_room
} = require("../../database/schemas");

exports.fetchActionLog = async () => {
	return app_logs.findAndCountAll({
		order: [['created_at', 'DESC']]
	});
}

exports.createActionLog = async (request) => {
	return app_logs.create(request);
}

/**
 * Helper function to capture affected fields from request payload
 * @param {Object} requestBody - The request body containing the fields
 * @param {Array} excludeFields - Fields to exclude from tracking (optional)
 * @returns {Array} Array of field names that were provided in the request
 */
exports.captureAffectedFields = (requestBody, excludeFields = []) => {
	if (!requestBody || typeof requestBody !== 'object') {
		return [];
	}

	// Default fields to exclude from tracking
	const defaultExcludeFields = [
		'password',
		'confirm_password',
		'access_token',
		'refresh_token',
		'created_at',
		'updated_at',
		'is_deleted'
	];

	const fieldsToExclude = [...defaultExcludeFields, ...excludeFields];

	// Get all keys from request body and filter out excluded fields
	const affectedFields = Object.keys(requestBody).filter(field =>
		!fieldsToExclude.includes(field) &&
		requestBody[field] !== undefined &&
		requestBody[field] !== null &&
		requestBody[field] !== ''
	);

	return affectedFields;
}