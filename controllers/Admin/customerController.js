const customerModel = require("../../models/Admin/customerModel");
const companyModel = require("../../models/Admin/companyModel");
const commonModel = require("../../models/Admin/commonModel");
const commonFunction = require("../../assets/common");
const ActionLogModel = require("../../models/Admin/ActionLogModel");
const AWS = require("aws-sdk");
const bcrypt = require("bcrypt");
const crypto = require("crypto-js");
const fs = require("fs");
const { sendEmail, readFile } = require("../../assets/common");
const axios = require("axios");


exports.customerStorageIdUpdate = async (request, response) => {
	try {
		const customerDetail = await customerModel.customerStorageIdUpdate(request.body);
		if (customerDetail.includes(1)) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: "success",
			});
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: "fail",
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.customerStorageIdUpdate -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.getCustomerList = async (request, response) => {
	try {
		let customerList;
		if (request.company_id) {
			const getCompanyDetails = await companyModel.getIntegrationKeyDataCustomerFetch(request.company_id)
			let checkStatus = getCompanyDetails && getCompanyDetails.status ? getCompanyDetails.status : null
			if (checkStatus == "active") {
				customerList = await customerModel.getCustomerListActive(request.company_id, request.query);
			}
			else {
				customerList = await customerModel.getCustomerList(request.company_id, request.query);
			}
		}
		else {
			customerList = await customerModel.getCustomerList(request.company_id, request.query);
		}
		if (customerList) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: CUSTOMER_RETRIEVED_SUCCESS,
				data: { ...customerList },
			});
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: CUSTOMER_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.getCustomerList -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.addCustomerJson = async (request, response, CustomerData, consumerLoginJson, storageCompanyId) => {
	const addCustomerJson = JSON.stringify({
		importCustomerId: CustomerData.customer_id,
		firstName: (CustomerData.first_name !== undefined) ? CustomerData.first_name : "",
		lastName: (CustomerData.last_name !== undefined) ? CustomerData.last_name : "",
		address: {
			addressLine1: (CustomerData.address1 !== undefined) ? CustomerData.address1 : "",
			addressLine2: (CustomerData.address2 !== undefined) ? CustomerData.address2 : "",
			city: (CustomerData.city !== undefined) ? CustomerData.city : "",
			state: (CustomerData.state !== undefined) ? CustomerData.state : "",
			zipcode: (CustomerData.zipCode !== undefined) ? CustomerData.zipCode : "",
			country: (CustomerData.country !== undefined) ? CustomerData.country : "",
		},
		email: [
			(CustomerData.email !== undefined) ? CustomerData.email : "",
			(CustomerData.email2 !== undefined) ? CustomerData.email2 : "",
		],
		phoneNumber: [
			(CustomerData.phone !== undefined) ? CustomerData.phone : "",
			(CustomerData.phone2 !== undefined) ? CustomerData.phone2 : "",
			(CustomerData.phone3 !== undefined) ? CustomerData.phone3 : "",
		],
		accountId: (CustomerData.account_id !== undefined) ? CustomerData.account_id : "",
		accountName: (CustomerData.account_name !== undefined) ? CustomerData.account_name : "",
		salesRep: (CustomerData.sales_rep !== undefined) ? CustomerData.sales_rep : "",
		companyId: storageCompanyId,
		importedTags: [

		],
		moverInventoryCustomerId: CustomerData.customer_id,
		createdFromMoverInventory: true,
	})

	try {
		const addCustomerResponse = await axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/customers`, addCustomerJson, {
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
		if (addCustomerResponse) {
			let params = {
				customer_id: CustomerData.customer_id,
				customerIdStorage: addCustomerResponse.data.data.id
			};

			const customerDetail = await customerModel.customerStorageIdUpdate(params);
			return customerDetail
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer add Fail", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer add Fail", {});
	}
}

exports.addCsvCustomer = async (request, response) => {
	try {
		const { customerData, integrationKeyStatus, integrationKey, storageCompanyId, companyId } = request.body
		for (var i = 0; i < customerData.length; i++) {
			const customer = customerData[i];
			const emailCount = await customerModel.checkReExistingEmailAddEmail(customer);
			if (emailCount === 0) {
				customer.company_id = companyId
				const customerDetail = await customerModel.addCustomer(customer)
				if (integrationKeyStatus) {
					const consumerLoginJson = await this.consumerLoginJsonFun(request, response, integrationKey);
					const addCustomerJson = await this.addCustomerJson(request, response, customerDetail, consumerLoginJson, storageCompanyId)
				}
			}
		}
		response.status(SUCCESS_CODE).json({
			status: 1,
			message: CUSTOMER_ADD_SUCCESS,
			data: {},
		});
	} catch (error) {
		console.log("exports.addCsvCustomer -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.addCustomer = async (request, response) => {
	try {
		const emailCheckCompany = await companyModel.checkExistingEmail(
			request.body.email
		);
		let getUserDetails = await commonModel.getUserDetails(request);

		if (emailCheckCompany > 0) {
			return response.status(CONFLICT_CODE).json({
				status: 1,
				message: "Email can't be same as company email, so please choose another!. ",
				data: {},
			})
		}
		const emailCount = await customerModel.checkReExistingEmailAddTime(request.body);
		if (emailCount > 0) {
			response.status(CONFLICT_CODE).json({
				status: 1,
				message: CUSTOMER_EMAIL_EXIST,
				data: {},
			});
		} else {
			let newFileName = "";
			let headerData = await commonFunction.jwtTokenDecode(request.headers.access_token);
			if (request.file && request.file.originalname !== "") {
				const fileName = request.file.filename;
				const fileExt = request.file.originalname.split(".");
				newFileName = fileName + "." + fileExt[1];
				const s3 = new AWS.S3();
				let params = {
					ACL: "public-read",
					Bucket: Const_AWS_BUCKET,
					Body: fs.createReadStream(request.file.path),
					Key: Const_AWS_Customer_Profile + "original/" + newFileName,
				};
				let result = await commonFunction.UploadImageS3(params, s3);
				if (result) {
					fs.unlinkSync(request.file.path);
				}
			}
			try {

				const customerDetail = await customerModel.addCustomer(request.body, newFileName)
				//newChanges
				if (request.body.tag) {
					let tagCustomer = [];
					request.body.tag.map((tag) =>
						tagCustomer.push({
							customer_id: customerDetail.customer_id,
							tag_id: tag,
						})
					);
					await customerModel.addTagToCustomerModel(tagCustomer, customerDetail.customer_id);
				}

				ActionLogModel.createActionLog({
					platform: "CMS",
					performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
					performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
					performed_by_name: getUserDetails.name,
					action_type: "CUSTOMER_CREATE",
					action_performed_on_id: customerDetail.customer_id,
					action_performed_on_name: `${customerDetail.first_name} ${customerDetail.last_name}`,
				})

				response.status(SUCCESS_CODE).json({
					status: 1,
					message: CUSTOMER_ADD_SUCCESS,
					data: customerDetail,
				});
			} catch (error) {
				response.status(SERVER_ERROR_CODE).json({
					status: 0,
					message: error.message,
					data: {},
				});
			}
		}
	} catch (error) {
		console.log("exports.addCustomer -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.alreadyCustomeCheckStorage = async (request, response) => {
	try {
		const emailCount = await customerModel.checkReExistingEmailAdd(request.body);
		if (emailCount.length > 0) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: CUSTOMER_EMAIL_EXIST,
				data: true,
			});
		} else {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: "Success",
				data: false,
			});
		}
	}
	catch (error) {
		console.log("exports.alreadyCustomeCheckStorage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.alreadyCustomeCheckStorageEdit = async (request, response) => {
	try {
		const emailCount = await customerModel.checkReExistingEmailEdit(request.body);
		if (emailCount.length > 0) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: CUSTOMER_EMAIL_EXIST,
				data: true,
			});
		} else {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: "Success",
				data: false,
			});
		}
	}
	catch (error) {
		console.log("exports.alreadyCustomeCheckStorageEdit -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.addCustomerStorage = async (request, response) => {
	try {

		const getCompanyIdForStaff = await companyModel.getCompanyIdForStaff(request.body);
		request.body.company_id = getCompanyIdForStaff.company_id


		const emailCheckCompany = await companyModel.checkExistingEmail(
			request.body.email
		);

		if (emailCheckCompany > 0) {
			return response.status(CONFLICT_CODE).json({
				status: 1,
				message: "Email can't be same as company email, so please choose another!. ",
				data: {},
			})
		}
		const emailCount = await customerModel.checkReExistingEmailAddEmail(request.body);
		if (emailCount > 0) {
			response.status(CONFLICT_CODE).json({
				status: 1,
				message: CUSTOMER_EMAIL_EXIST,
				data: {},
			});
		} else {

			try {
				const customerDetail = await customerModel.addCustomerStorage(request.body)
				response.status(SUCCESS_CODE).json({
					status: 1,
					message: CUSTOMER_ADD_SUCCESS,
					data: customerDetail,
				});
			} catch (error) {
				response.status(SERVER_ERROR_CODE).json({
					status: 0,
					message: error.message,
					data: {},
				});
			}
		}
	} catch (error) {
		console.log("exports.addCustomerStorage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};


exports.viewCustomer = async (request, response) => {
	try {
		const customerDetail = await customerModel.viewCustomer(
			request.company_id,
			request.body.customer_id
		);
		if (customerDetail !== "") {
			response.status(SUCCESS_CODE).json({
				message: CUSTOMER_RETRIEVED_SUCCESS,
				data: customerDetail,
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: CUSTOMER_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.viewCustomer -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.editCustomer = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const viewCustomer = await customerModel.findCustomer(request.body.customer_id);
		const emailCount = await customerModel.checkReExistingEmail(request.body);
		if (emailCount > 0) {
			response.status(CONFLICT_CODE).json({
				status: 1,
				message: CUSTOMER_EMAIL_EXIST,
				data: {},
			});
		}
		else {
			const s3 = new AWS.S3();
			try {
				let headerData = await commonFunction.jwtTokenDecode(request.headers.access_token);
				if (request.body.retain_photo === "false") {
					const oldCustomerData = await customerModel.viewCustomer(
						request.company_id,
						request.body.customer_id
					);
					if (oldCustomerData.photo) {
						let delMedia = {
							Bucket: Const_AWS_BUCKET,
							Key: Const_AWS_Customer_Profile + "original/" + oldCustomerData.photo,
						};
						await commonFunction.deleteImageS3(delMedia, s3);
					}
				}
				if (request.file && request.file.originalname !== "") {
					let newFileName = "";
					const oldCustomerData = await customerModel.viewCustomer(
						request.company_id,
						request.body.customer_id
					);
					const fileName = request.file.filename;
					const fileExt = request.file.originalname.split(".");
					newFileName = fileName + "." + fileExt[1];

					let params = {
						ACL: "public-read",
						Bucket: Const_AWS_BUCKET,
						Body: fs.createReadStream(request.file.path),
						Key: Const_AWS_Customer_Profile + "original/" + newFileName,
					};
					if (oldCustomerData.photo) {
						let delMedia = {
							Bucket: Const_AWS_BUCKET,
							Key: Const_AWS_Customer_Profile + "original/" + oldCustomerData.photo,
						};
						await commonFunction.deleteImageS3(delMedia, s3);
					}

					let result = await commonFunction.UploadImageS3(params, s3);

					if (result) {
						fs.unlinkSync(request.file.path);
						request.body.photo = newFileName;
					}
				}

				const customerCount = await customerModel.checkExistingCustomer(request.body);
				if (customerCount > 0) {
					try {
						const customerDetail = await customerModel.editCustomer(request.body);
						//newChanges
						if (request.body.tag) {
							let tagCustomer = [];
							request.body.tag.map((tag) =>
								tagCustomer.push({
									customer_id: request.body.customer_id,
									tag_id: tag,
								})
							);
							await customerModel.addTagToCustomerModel(tagCustomer, request.body.customer_id);
						}

						ActionLogModel.createActionLog({
							platform: "CMS",
							performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
							performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
							performed_by_name: getUserDetails.name,
							action_type: "CUSTOMER_UPDATE",
							action_performed_on_id: request.body.customer_id,
							action_performed_on_name: `${viewCustomer.first_name} ${viewCustomer.last_name}`,
						})

						response.status(SUCCESS_CODE).json({
							status: 1,
							message: CUSTOMER_UPDATED_SUCCESS,
							data: {},
						});
					} catch (error) {
						response.status(EXPECTATION_FAILED_CODE).json({
							status: 0,
							message: CUSTOMER_UPDATE_FAIL,
							data: {},
						});
					}
				} else {
					response.status(SUCCESS_CODE).json({
						status: 0,
						message: CUSTOMER_NOT_FOUND,
						data: {},
					});
				}
			} catch (error) {
				response.status(EXPECTATION_FAILED_CODE).json({
					status: 0,
					message: CUSTOMER_UPDATE_FAIL,
					data: {},
				});
			}
		}
	} catch (error) {
		console.log("exports.editCustomer -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};


exports.customerDeleteJson = async (request, response, fetchCustomerForStorage, consumerLoginJson) => {
	const customerId = fetchCustomerForStorage.storage_customer_id
	const customerDeleteJson = JSON.stringify({
		deleted: true,
		isActive: 0
	});
	try {
		const customerDeleteResponse = await axios.put(`${MOVER_STORAGE_API_URL}import/mover-inventory/customers/active-status/${customerId}`, customerDeleteJson, {
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
		if (customerDeleteResponse.status == 200) {
			return customerDeleteResponse
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer delete fail", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer delete fail", {});
	}
}

exports.deleteCustomer = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const viewCustomer = await customerModel.findCustomer(request.body.customer_id);
		const customerCount = await customerModel.checkExistingCustomer(request.body);
		if (customerCount > 0) {

			const fetchCustomerForStorage = await customerModel.fetchCustomerForStorage(request.body);
			const integration_key = fetchCustomerForStorage.dataValues.integration_key ? fetchCustomerForStorage.dataValues.integration_key : fetchCustomerForStorage.integration_key

			if (fetchCustomerForStorage.storage_customer_id !== undefined && fetchCustomerForStorage.storage_customer_id !== null && fetchCustomerForStorage.storage_customer_id !== "") {
				const consumerLoginJson = await this.consumerLoginJsonFun(request, response, integration_key);
				const customerDeleteJson = await this.customerDeleteJson(request, response, fetchCustomerForStorage, consumerLoginJson)
			}

			const customerDetail = await customerModel.deleteCustomer(request.body);
			if (customerDetail.includes(1)) {
				ActionLogModel.createActionLog({
					platform: "CMS",
					performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
					performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
					performed_by_name: getUserDetails.name,
					action_type: "CUSTOMER_DELETE",
					action_performed_on_id: request.body.customer_id,
					action_performed_on_name: `${viewCustomer.first_name} ${viewCustomer.last_name}`,
				})
				response.status(SUCCESS_CODE).json({
					message: CUSTOMER_DELETE_SUCCESS,
				});
			} else {
				response.status(EXPECTATION_FAILED_CODE).json({
					message: CUSTOMER_DELETE_FAIL,
					data: {},
				});
			}
		} else {
			response.status(SUCCESS_CODE).json({
				status: 0,
				message: CUSTOMER_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.deleteCustomer -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};


exports.consumerLoginJsonFun = async (request, response, integration_key) => {
	const consumerLoginJson = JSON.stringify({
		companyIdTokenMoverInventory: integration_key,
		email: "<EMAIL>",
		password: "5PLaRAqq",
		deviceToken: "abcd",
		deviceType: 0,
	});
	try {
		const consumerLoginResponse = await axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson, {
			headers: {
				'Content-Type': 'application/json'
			}
		})
		if (consumerLoginResponse.data !== "" && consumerLoginResponse.data !== undefined && consumerLoginJson.data !== null) {
			return consumerLoginResponse.data;
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
	}
}



exports.customerStatusJson = async (request, response, fetchCustomerForStorage, consumerLoginJson) => {
	const customerId = fetchCustomerForStorage.storage_customer_id
	const customerStatusJson = JSON.stringify({
		isDeleted: false,
		isActive: fetchCustomerForStorage.status == "active" ? 1 : 0
	});
	try {
		const customerStatusResponse = await axios.put(`${MOVER_STORAGE_API_URL}import/mover-inventory/customers/active-status/${customerId}`, customerStatusJson, {
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
		if (customerStatusResponse.status == 200) {
			return customerStatusResponse
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer status fail 1", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer status fail 2", {});
	}
}

exports.changeCustomerStatus = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const viewCustomer = await customerModel.findCustomer(request.body.customer_id);
		const customerCount = await customerModel.checkExistingCustomer(request.body);
		if (customerCount > 0) {
			try {
				const customerDetail = await customerModel.changeCustomerStatus(request.body);
				if (customerDetail.includes(1)) {
					const fetchCustomerForStorage = await customerModel.fetchCustomerForStorage(request.body);
					const integration_key = fetchCustomerForStorage.dataValues.integration_key ? fetchCustomerForStorage.dataValues.integration_key : fetchCustomerForStorage.integration_key

					if (fetchCustomerForStorage.storage_customer_id !== undefined && fetchCustomerForStorage.storage_customer_id !== null && fetchCustomerForStorage.storage_customer_id !== "") {
						const consumerLoginJson = await this.consumerLoginJsonFun(request, response, integration_key);
						const customerStatusJson = await this.customerStatusJson(request, response, fetchCustomerForStorage, consumerLoginJson)
					}
					ActionLogModel.createActionLog({
						platform: "CMS",
						performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
						performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
						performed_by_name: getUserDetails.name,
						action_type: viewCustomer.status == "active" ? "CUSTOMER_DEACTIVATE" : "CUSTOMER_ACTIVATE",
						action_performed_on_id: request.body.customer_id,
						action_performed_on_name: `${viewCustomer.first_name} ${viewCustomer.last_name}`,
					})
					response.status(SUCCESS_CODE).json({
						message: STATUS_CHANGE_SUCCESS,
					});
				} else {
					response.status(EXPECTATION_FAILED_CODE).json({
						message: STATUS_CHANGE_FAIL,
						data: {},
					});
				}
			} catch (error) {
				response.status(EXPECTATION_FAILED_CODE).json({
					message: STATUS_CHANGE_FAIL,
					data: {},
				});
			}
		} else {
			response.status(SUCCESS_CODE).json({
				status: 0,
				message: CUSTOMER_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.changeCustomerStatus -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.changeCustomerStatusStorage = async (request, response) => {
	try {
		try {
			const customerDetail = await customerModel.changeCustomerStatusStorage(request.body);
			if (customerDetail.includes(1)) {
				response.status(SUCCESS_CODE).json({
					status: 1,
					message: STATUS_CHANGE_SUCCESS,
					data: {},
				});
			} else {
				response.status(EXPECTATION_FAILED_CODE).json({
					status: 0,
					message: STATUS_CHANGE_FAIL,
					data: {},
				});
			}
		} catch (error) {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: STATUS_CHANGE_FAIL,
				data: {},
			});
		}


	} catch (error) {
		console.log("exports.changeCustomerStatusStorage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.isValidActiveCustomerController = async (request, response, next) => {
	try {
		const { email } = request.body;
		const responseData = await customerModel.isValidActiveCustomerModel(email);
		if (!responseData) {
			response.status(SERVER_ERROR_CODE).json({
				status: 0,
				message: CUSTOMER_NOT_FOUND,
				data: {},
			});
		}
		else if (responseData.status === "inactive") {
			response.status(SERVER_ERROR_CODE).json({
				status: 0,
				message: INACTIVE_CUSTOMER_LOGIN_FOR_CUSTOMER_PORTAL,
				data: {},
			});
		}
		else {
			next();
		}
	}
	catch (error) {
		console.log("exports.isValidActiveCustomerController -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.isValidCustomerController = async (request, response, next) => {
	try {
		const customerId = request.body.customer_id
			? request.body.customer_id
			: request.params.customerId;
		const email = request.body.email;
		if (await customerModel.isCustomerModel(customerId, email)) {
			if (email) request.model_customer_id = await customerModel.getCustomerId(email);
			next();
		} else {
			commonFunction.generateResponse(response, NOT_FOUND_CODE, 0, CUSTOMER_NOT_FOUND, {});
		}
	}
	catch (error) {
		console.log("exports.isValidCustomerController -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.isValidCustomerControllerStorage = async (request, response, next) => {
	try {

		const getCustomerIdForStorage = await customerModel.getCustomerIdForStorage(request.body);
		request.body.customer_id = getCustomerIdForStorage.customer_id
		request.body.company_id = getCustomerIdForStorage.company_id

		const customerId = request.body.customer_id
			? request.body.customer_id
			: request.params.customerId;
		const email = request.body.email;
		if (await customerModel.isCustomerModel(customerId, email)) {
			if (email) request.model_customer_id = await customerModel.getCustomerId(email);
			next();
		} else {
			commonFunction.generateResponse(response, NOT_FOUND_CODE, 0, CUSTOMER_NOT_FOUND, {});
		}
	}
	catch (error) {
		console.log("exports.isValidCustomerControllerStorage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.isSignUpController = async (request, response, next) => {
	try {
		const customerId = request.model_customer_id;
		await customerModel.updateCustomerInviteStatusCustomerModel(customerId, "VIEWED");
		const customerDetails = await customerModel.isSignUpModel(customerId);
		if (customerDetails) {
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				CUSTOMER_SET_PASSWORD,
				{ sign_up_flag: customerDetails }
			)
		}
		else {
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				CUSTOMER_SIGN_IN,
				{ sign_up_flag: customerDetails }
			)
		}
	} catch (error) {
		commonFunction.generateResponse(
			response,
			EXPECTATION_FAILED_CODE,
			0,
			GENERIC_REQUEST_FAILURE,
			{}
		);
	}
};

exports.isforgotPasswordController = async (request, response, next) => {
	try {
		const customerId = request.model_customer_id;
		await customerModel.updateCustomerPasswordStatusCustomerModel(customerId);

		let customer = await customerModel.findCustomer(customerId);
		let hashedID = crypto.AES.encrypt(customer.email, "Openxcell").toString();
		let html = await readFile("ForgotPassword.html");
		html = html.replace(/{{action_link}}/g, RESET_CUSTOMER_PASSWORD_LINK + `/${hashedID}`);
		html = html.replace(/{{name}}/g, customer.first_name);
		html = html.replace(
			/{{company_name}}/g,
			`${customer && customer.customer_company ? customer.customer_company.company_name : ""}`
		);
		html = html.replace(
			/{{company_email}}/g,
			`${customer && customer.customer_company ? customer.customer_company.email : ""}`
		);
		html = html.replace(
			/{{company_phone}}/g,
			`${customer && customer.customer_company ? customer.customer_company.phone : ""}`
		);

		let isEmailSent = await sendEmail(customer.email, "Reset Password", html);

		if (isEmailSent === false) {
			throw Error("Unable to send email");
		} else {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: FORGOT_EMAIL_SENT,
				data: { email: customer.email },
			});
		}
	} catch (error) {
		console.log("exports.isforgotPasswordController -> error: ", error);
		commonFunction.generateResponse(
			response,
			EXPECTATION_FAILED_CODE,
			0,
			GENERIC_REQUEST_FAILURE,
			{}
		);
	}
};

exports.customerBasicController = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const getCompanyDetails = await companyModel.getIntegrationKeyDataCustomer(getUserDetails)
		let checkStatus = getCompanyDetails && getCompanyDetails.status ? getCompanyDetails.status : null
		request.query.search = request.query.search ? request.query.search : "";

		let getCustomerBasicData;

		if (checkStatus == "active") {
			getCustomerBasicData = await customerModel.getCustomerBasicModelActive(request.query, getUserDetails)
		}
		else {
			getCustomerBasicData = await customerModel.getCustomerBasicModel(request.query, getUserDetails)
		}

		if (getCustomerBasicData && getCustomerBasicData.length > 0)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				CUSTOMER_RETRIEVED_SUCCESS,
				getCustomerBasicData
			);
		else commonFunction.generateResponse(response, NOT_FOUND_CODE, 3, CUSTOMER_LIST_EMPTY, {});
	}
	catch (reason) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
};

exports.mailShipmentDetailToCustomerController = async (request, response, next) => {
	try {
		const { model_customer_id, model_customer_email } = request.body;
		if (!model_customer_id)
			return commonFunction.generateResponse(response, SUCCESS_CODE, 0, CUSTOMER_NOT_FOUND, {});

		let html = await commonFunction.readFile("customerPortal.html");

		const customerDetails = await customerModel.findCustomer(model_customer_id)

		if (customerDetails.email) {
			html = html.replace(
				/{{customer_name}}/g,
				`${customerDetails.first_name ? customerDetails.first_name : ""} ${customerDetails.last_name ? customerDetails.last_name : ""
				}`
			);
			html = html.replace(
				/{{company_name}}/g,
				`${customerDetails && customerDetails.customer_company
					? customerDetails.customer_company.company_name
					: ""
				}`
			);
			html = html.replace(
				/{{company_email}}/g,
				`${customerDetails && customerDetails.customer_company
					? customerDetails.customer_company.email
					: ""
				}`
			);
			html = html.replace(
				/{{company_phone}}/g,
				`${customerDetails && customerDetails.customer_company
					? customerDetails.customer_company.phone
					: ""
				}`
			);
			html = html.replace(/{{CUSTOMER_PORTAL_LINK}}/g, `${CUSTOMER_PORTAL_LINK}?email=${customerDetails.email}`);
			//newChanges
			const returnResponse = await sendEmail(model_customer_email, "Your customer portal link", html);
			if (returnResponse) {
				next()
			}

		} else {
			return commonFunction.generateResponse(response, SUCCESS_CODE, 0, MAIL_NOT_FOUND, {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.alterCustomerPasswordController = async (request, response, next) => {
	try {
		const { model_customer_id } = request;
		const { password } = request.body;
		const customerDetails = await customerModel.updateCustomerPasswordModel(model_customer_id, password);
		if (customerDetails) {
			await customerModel.updateCustomerInviteStatusCustomerModel(model_customer_id, "REGISTERED");
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				PASSWORD_CHANGE_SUCCESS,
				customerDetails
			);
		};
	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};


exports.changeCustomerPasswordController = async (request, response, next) => {
	try {
		const { model_customer_id } = request;
		const { password } = request.body;
		const customerDetails = await customerModel.updateCustomerPasswordModelForForget(model_customer_id, password);
		if (customerDetails[0] !== 0) {
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				PASSWORD_CHANGE_SUCCESS,
				customerDetails
			);
		} else {
			commonFunction.generateResponse(
				response,
				SERVER_ERROR_CODE,
				0,
				"Session is Expired Please Send Mail Again!",
				{}
			);
		}
	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Something Went Wrong!", {});
	}
};

exports.updateCustomerMailStatusController = async (request, response, next) => {
	try {
		const { model_customer_id } = request.body;
		if (request.route.path === `/customer/:shipmentId/job`) {
			await customerModel.updateCustomerInviteStatusCustomerModel(model_customer_id, "REGISTERED");
			next();
		} else {
			const updateStatus = await customerModel
				.updateCustomerInviteStatusCustomerModel(model_customer_id, "INVITED");
			if (updateStatus) {
				commonFunction.generateResponse(response, SUCCESS_CODE, 1, MAIL_SENT_TO_CUSTOMER, updateStatus)
			}
		}
	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.signIn = async (request, response) => {
	try {
		const customerDetails = await customerModel.getCustomerPasswordAndShipmentDetailModel(
			request.body
		);

		if (customerDetails.password && customerDetails.password !== "") {
			if (!bcrypt.compareSync(request.body.password, customerDetails.password)) {
				return commonFunction.generateResponse(
					response,
					EXPECTATION_FAILED_CODE,
					0,
					LOGIN_PASSWORD_FAIL,
					{}
				);
			}
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				LOGIN_SUCCESS,
				customerDetails.shipment_jobs
			);
		} else {
			commonFunction.generateResponse(response, EXPECTATION_FAILED_CODE, 0, LOGIN_EMAIL_FAIL, {});
		}
	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.checkCustomerAssignToJob = async (request, response, next) => {
	const { count } = await customerModel.checkCustomerAssignToJob(request.body.customer_id);
	if (count && count > 0) {
		commonFunction.generateResponse(
			response,
			EXPECTATION_FAILED_CODE,
			0,
			CUSTOMER_DELETE_ASSIGN_JOB,
			{}
		);
	} else {
		next();
	}
};

exports.checkCustomerIsValidToUpdateStatus = async (request, response, next) => {
	const { count } = await customerModel.checkCustomerAssignToJobForStatus(request.body.customer_id);
	if (count && count > 0) {
		const findOldStatusOfCustomer = await customerModel.findOldStatusOfCustomer(request.body.customer_id);
		if (findOldStatusOfCustomer.status === "active") {
			commonFunction.generateResponse(
				response,
				EXPECTATION_FAILED_CODE,
				0,
				CUSTOMER_STATUS_UPDATE_ASSIGN_JOB,
				{}
			);
		} else {
			next();
		}
	} else {
		next();
	}
};
