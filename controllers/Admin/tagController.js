const tagModel = require("../../models/Admin/tagModel");
const commonFunction = require("../../assets/common");
const commonModel = require("../../models/Admin/commonModel");
const ActionLogModel = require("../../models/Admin/ActionLogModel");


exports.getTagListingController = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		let getUserDetails = await commonModel.getUserDetails(request);
		const tagListing = await tagModel.getTagListingModel(request.query, getUserDetails)
		if (tagListing)
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_RETRIEVED_SUCCESS, tagListing)
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_NOT_FOUND, {})
	}
	catch (reason) {
		console.log("exports.getTagListingController -> error: ", reason);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}

exports.getTagForCustomerListingController = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const tagListing = await tagModel.getTagListingForCustomerModel(getUserDetails)
		if (tagListing)
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_RETRIEVED_SUCCESS, tagListing)
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_NOT_FOUND, {})
	}
	catch (reason) {
		console.log("exports.getTagForCustomerListingController -> error: ", reason);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}

exports.getTagForShipmentListingController = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const tagListing = await tagModel.getTagListingForShipmentModel(getUserDetails)
		if (tagListing)
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_RETRIEVED_SUCCESS, tagListing)
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_NOT_FOUND, {})
	}
	catch (reason) {
		console.log("exports.getTagForShipmentListingController -> error: ", reason);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}

exports.createTagController = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const tagDetails = await tagModel.createTagModel(request.body, getUserDetails)
		if (tagDetails) {
			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "TAG_CREATE",
				action_performed_on_id: tagDetails.tag_id,
				action_performed_on_name: tagDetails.name,
			})
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_ADDED_SUCCESS, tagDetails)
		}
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_ADDED_FAILURE, {})
	}
	catch (reason) {
		console.log("exports.createTagController -> error: ", reason);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}
exports.editTagController = async (request, response) => {
	try {
		const { tagId } = request.params
		let getUserDetails = await commonModel.getUserDetails(request);
		const tagDetails = await tagModel.editTagModel(tagId, request.body, getUserDetails)
		if (tagDetails) {
			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "TAG_UPDATE",
				action_performed_on_id: tagId,
				action_performed_on_name: request.body.name,
			})
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_EDIT_SUCCESS, tagDetails)
		}
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_EDIT_FAILURE, {})
	}
	catch (reason) {
		console.log("exports.editTagController -> error: ", reason);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}

exports.removeTagController = async (request, response) => {
	try {
		const { tagId } = request.params
		const data = await tagModel.removeTagModel(tagId)
		let getUserDetails = await commonModel.getUserDetails(request);
		if (data) {
			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "TAG_DELETE",
				action_performed_on_id: tagId,
				action_performed_on_name: data.name,
			})
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_DELETE_SUCCESS, {})
		}
	}
	catch (reason) {
		console.log("exports.removeTagController -> error: ", reason);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}

exports.isValidTagController = async (request, response, next) => {
	try {
		const tag = request.body ? (request.body.tag ? request.body.tag : []) : []
		if (request.params.tagId)
			tag.push(request.params.tagId ? request.params.tagId : request.body.tag_id)
		//newChanges
		tag.map(async (tagId) => {
			const isValidTag = await tagModel.checkTagExistenceModel(tagId);
			if (!isValidTag)
				return commonFunction.generateResponse(response, NOT_FOUND_CODE, 0, TAG_NOT_FOUND, {})
		})
		next()
	} catch (error) {
		console.log("exports.isValidTagController -> error: ", error);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {})
	}
}

exports.isTagAssignToItemController = async (request, response, next) => {
	try {
		const { type } = request.body;
		const { tagId } = request.params
		const { count } = await tagModel.checkTagAssignModel(tagId, type)
		if (count && count > 0) {
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, "Cannot delete this tag as this tag is assign to item", {});
		} else {
			next();
		}
	} catch (error) {
		console.log("exports.isTagAssignToItemController -> error: ", error);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {})
	}
}

exports.viewTagController = async (request, response) => {
	try {
		const { tagId } = request.params
		const tagDetails = await tagModel.getTagModel(tagId)
		if (tagDetails)
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_DETAILS, tagDetails)
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_NOT_FOUND, {})
	}
	catch (reason) {
		console.log("exports.viewTagController -> error: ", reason);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}
