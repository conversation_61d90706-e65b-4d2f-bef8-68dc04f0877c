const moment = require("moment");
const fs = require("fs");
const shipmentModel = require("../../models/Admin/shipmentModel");
const shipmentTypeModel = require("../../models/Admin/shipmentTypeModel");
const commonModel = require("../../models/Admin/commonModel");
const axios = require("axios");
const ExcelJS = require("exceljs");
const path = require("path");
const createCsvWriter = require("csv-write-stream");
const commonFunction = require("../../assets/common");
const { sendEmail, readFile } = require("../../assets/common");
const AWS = require("aws-sdk");
const ActionLogModel = require("../../models/Admin/ActionLogModel");

const PDFGeneration = require("../../assets/pdfHandler");
const {
  generateResponse,
  randomSequencedString,
} = require("../../assets/common");
const { request } = require("http");

exports.getAssignShipmentTypeStages = async (request, response) => {
  try {
    const { shipmentId } = request.body;
    const getAssignShipmentTypeStages =
      await shipmentModel.getAssignShipmentTypeStages(shipmentId);
    if (getAssignShipmentTypeStages) {
      generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SHIPMENT_TYPE_RETRIEVED_SUCCESS,
        getAssignShipmentTypeStages
      );
    } else {
      generateResponse(response, SUCCESS_CODE, 0, SHIPMENT_TYPE_NOT_FOUND, {});
    }
  } catch (error) {
    console.log("exports.getAssignShipmentTypeStages -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.addShipmentDocumentsController = async (request, response) => {
  try {
    const { shipmentId } = request.params;
    let newFileName = "";
    const newFileList = [];

    if (request.files && request.files.file && request.files.file.length > 0) {
      const s3 = new AWS.S3();

      for (const key in request.files.file) {
        if (!Object.hasOwnProperty.call(request.files.file, key)) continue;

        const element = request.files.file[key];
        const fileName = element.filename;
        const fileExt = fileName.split(".").pop();

        const newFileName = `${fileName}.${fileExt}`;
        const params = {
          ACL: "public-read",
          Bucket: Const_AWS_BUCKET,
          Body: fs.createReadStream(element.path),
          Key: `${Const_AWS_Shipment_Document}original/${newFileName}`,
        };

        try {
          const result = await commonFunction.UploadImageS3(params, s3);
          if (result) {
            fs.unlinkSync(element.path);
          }

          newFileList.push({
            media: newFileName,
            name: element.originalname,
          });
        } catch (error) {
          // Handle errors here
          console.error("Error uploading file to S3:", error);
        }
      }
    }

    const addDocument = await shipmentModel.addDocument(
      shipmentId,
      newFileList
    );

    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "PDF uploaded successfully",
    });
  } catch (error) {
    console.log("exports.addShipmentDocumentsController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.deleteShipmentDocumentsController = async (request, response) => {
  try {
    const { pdfId } = request.body;
    const deleteDocument = await shipmentModel.deleteDocument(pdfId);
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "PDF deleted successfully",
    });
  } catch (error) {
    console.log("exports.addShipmentDocumentsController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.webhookShipmentDetails = async (request, response) => {
  try {
    const { shipmentId } = request.params;
    const shipmentUpdateData = {
      shipment_job_id: request.body.shipment_job_id,
      storage_shipment_job_id: request.body.id,
      shipment_name: request.body.name,
      warehouseId: request.body.warehouseInfo.id,
      total_volume: request.body.volume,
      total_weight: request.body.weight,
      // is_job_complete_flag: request.body.status == "Delivered" ? 1 : 0,
      source: request.body.source,
      opportunity_reference: request.body.opportunityReference,
      contact_reference: request.body.contactReference,
      account_reference: request.body.accountReference,
      pickup_address: request.body.originAddress.addressLine1,
      pickup_address2: request.body.originAddress.addressLine2,
      pickup_city: request.body.originAddress.city,
      pickup_state: request.body.originAddress.state,
      pickup_zipcode: request.body.originAddress.zipcode,
      pickup_country: request.body.originAddress.country,
      delivery_address: request.body.destinationAddress.addressLine1,
      delivery_address2: request.body.destinationAddress.addressLine2,
      delivery_city: request.body.destinationAddress.city,
      delivery_state: request.body.destinationAddress.state,
      delivery_zipcode: request.body.destinationAddress.zipcode,
      delivery_country: request.body.destinationAddress.country,
    };

    const updateShipmentWebhook = await shipmentModel.updateShipmentWebhook(
      shipmentUpdateData,
      shipmentId
    );
    response.send();
  } catch (error) {
    console.log("exports.webhookShipmentDetails -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.webhookUnitDetails = async (request, response) => {
  try {
    const { unitId } = request.params;
    const {
      storage_unit_id,
      shipment_job_id,
      unitCode,
      number,
      currentLocation,
      numericLocation,
      addedBy,
      name,
      status,
      isActive,
      warehouseId,
      customerId,
      shipmentId,
      roomId,
      unitTypeId,
      isDeleted,
    } = request.body;

    if (isDeleted) {
      await shipmentModel.DeleteUnitWebhook(unitId);
      return response.status(200).json({
        status: 1,
        message: "Unit deleted successfully",
        data: {},
      });
    }

    const unitUpdateData = {
      storage_unit_id,
      shipment_job_id,
      unitCode,
      number,
      currentLocation,
      numericLocation,
      addedBy,
      name,
      status,
      isActive,
      warehouseId,
      customerId,
      shipmentId,
      roomId,
      unitTypeId,
    };

    await shipmentModel.updateUnitWebhook(unitUpdateData, unitId);

    return response.status(200).json({
      status: 1,
      message: "Unit updated successfully",
      data: {},
    });

  } catch (error) {
    return response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message || "Internal server error",
      data: {},
    });
  }
};

exports.shipmentStorageIdUpdate = async (request, response) => {
  try {
    const shipmentDetail = await shipmentModel.shipmentStorageIdUpdate(
      request.body
    );
    if (shipmentDetail.includes(1)) {
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: "success",
      });
    } else {
      response.status(EXPECTATION_FAILED_CODE).json({
        status: 0,
        message: "fail",
        data: {},
      });
    }
  } catch (error) {
    console.log("exports.shipmentStorageIdUpdate -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

countException = (data) => {
  let count = 0;
  data.forEach((data) => {
    if (data.exceptions.length > 0) {
      count = count + 1;
    }
  });
  return count;
};

toDate = (date, checkPDFTime) => {
  if (date) {
    if (
      checkPDFTime === 1 ||
      checkPDFTime === "true" ||
      checkPDFTime === true
    ) {
      let s = new Date(date).toLocaleDateString([], {
        day: "numeric",
        month: "short",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
      return s;
    } else {
      let s = new Date(date).toLocaleDateString([], {
        day: "numeric",
        month: "short",
        year: "numeric",
      });
      return s;
    }
  } else {
    return null;
  }
};

function countfirearm(data) {
  let count = 0;
  data.forEach((data) => {
    if (data.is_firearm === "true" || data.is_firearm === true) {
      count = count + 1;
    } else {
      return count;
    }
  });
  return count;
}
exports.csvExportShipmentInventoryController = async (request, response) => {
  try {
    const { shipmentId } = request.body;
    const myData = await shipmentModel.getShipmentDetailModelForPdf(shipmentId);

    const inventoryData = await shipmentModel.getInventoryListForPDF(
      shipmentId
    );

    const shipmentName = `${myData.job_number}-${myData.shipment_name
      .replace(/[#]/g, "")
      .replace(/\.\./g, "")}`;

    let sortArray = inventoryData;
    let newArray = await sortArray.sort(function (a, b) {
      const nameA = a.item_qr.type.toUpperCase();
      const nameB = b.item_qr.type.toUpperCase();
      if (nameA > nameB) {
        return -1;
      }
      if (nameA < nameB) {
        return 1;
      }
      return 0;
    });
    let aginNewArray = await sortArray.sort(function (a, b) {
      const nameA = a.isManualLabel;
      const nameB = b.isManualLabel;
      if (nameA > nameB) {
        return 1;
      }
      if (nameA < nameB) {
        return -1;
      }
      return 0;
    });

    myData.job_items = await [];
    myData.job_items = await [...aginNewArray];

    const headerFont = {
      name: "Arial",
      bold: true,
      size: 12,
    };

    const workbook = new ExcelJS.Workbook();
    const shipmentDetails = workbook.addWorksheet("Shipment Details");
    const shipmentDetailsHeaders = [
      "Shipment Name",
      "Customer Name",
      "Origin Address",
      "Destination Address",
      "Total Items",
      "Total Cartons",
      "Total Volume",
      "Total Weight",
      "Disassembled Items",
      "Electronics",
      "High Value Items",
      "High Value Total",
      "Pro Gear Items",
      "Pro Gear Weight",
      "Items With Exceptions",
      "Pads Used",
      "Firearms Total Qty",
      "Supervisor",
      "Worker",
    ];

    shipmentDetails.addRow(shipmentDetailsHeaders);
    const shipmentDetailsHeaderRow = shipmentDetails.getRow(1);
    shipmentDetailsHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    shipmentDetails.columns.forEach((column) => {
      column.width = 20;
    });

    const customerName =
      myData.customer_job.first_name + " " + myData.customer_job.last_name;
    const pickup_address1 =
      myData.pickup_address !== undefined &&
        myData.pickup_address !== null &&
        myData.pickup_address !== ""
        ? myData.pickup_address.replace(/,/g, "")
        : "";
    const pickup_address2 =
      myData.pickup_address2 !== undefined &&
        myData.pickup_address2 !== null &&
        myData.pickup_address2 !== ""
        ? " " + myData.pickup_address2.replace(/,/g, "")
        : "";
    const pickup_city =
      myData.pickup_city !== undefined &&
        myData.pickup_city !== null &&
        myData.pickup_city !== ""
        ? " " + myData.pickup_city.replace(/,/g, "")
        : "";
    const pickup_state =
      myData.pickup_state !== undefined &&
        myData.pickup_city !== null &&
        myData.pickup_city !== ""
        ? " " + myData.pickup_state.replace(/,/g, "")
        : "";
    const pickup_country =
      myData.pickup_country !== undefined &&
        myData.pickup_country !== null &&
        myData.pickup_country !== ""
        ? " " + myData.pickup_country.replace(/,/g, "")
        : "";
    const pickup_zipcode =
      myData.pickup_zipcode !== undefined &&
        myData.pickup_zipcode !== null &&
        myData.pickup_zipcode !== ""
        ? " " + myData.pickup_zipcode.replace(/,/g, "")
        : "";
    const pickup_address =
      pickup_address1 +
      pickup_address2 +
      pickup_city +
      pickup_state +
      pickup_country +
      pickup_zipcode;

    const delivery_address1 =
      myData.delivery_address !== undefined &&
        myData.delivery_address !== null &&
        myData.delivery_address !== ""
        ? myData.delivery_address.replace(/,/g, "")
        : "";
    const delivery_address2 =
      myData.delivery_address2 !== undefined &&
        myData.delivery_address2 !== null &&
        myData.delivery_address2 !== ""
        ? " " + myData.delivery_address2.replace(/,/g, "")
        : "";
    const delivery_city =
      myData.delivery_city !== undefined &&
        myData.delivery_city !== null &&
        myData.delivery_city !== ""
        ? " " + myData.delivery_city.replace(/,/g, "")
        : "";
    const delivery_state =
      myData.delivery_state !== undefined &&
        myData.delivery_state !== null &&
        myData.delivery_state !== ""
        ? " " + myData.delivery_state.replace(/,/g, "")
        : "";
    const delivery_country =
      myData.delivery_country !== undefined &&
        myData.delivery_country !== null &&
        myData.delivery_country !== ""
        ? " " + myData.delivery_country.replace(/,/g, "")
        : "";
    const delivery_zipcode =
      myData.delivery_zipcode !== undefined &&
        myData.delivery_zipcode !== null &&
        myData.delivery_zipcode !== ""
        ? " " + myData.delivery_zipcode.replace(/,/g, "")
        : "";
    const delivery_address =
      delivery_address1 +
      delivery_address2 +
      delivery_city +
      delivery_state +
      delivery_country +
      delivery_zipcode;

    const totalCartons = `${myData.total_cartons} (CP ${myData.total_cartons_cp} PBO ${myData.total_cartons_pbo})`;

    const totalVolume = Math.round(myData.total_volume) + " " + "Cu Ft";
    const totalWeight =
      myData.total_weight > 0 ? myData.total_weight + " " + "Lbs." : "0";
    const totalDisassembledItems = myData.total_disassembled_items
      ? myData.total_disassembled_items
      : "0";
    const totalElectronicsItems = myData.total_electronics_items
      ? myData.total_electronics_items
      : "0";
    const totalHighValueItems = myData.total_highValue_items
      ? myData.total_highValue_items
      : "0";
    const totalHighValue = myData.total_high_value
      ? "$" + " " + myData.total_high_value
      : "0";
    const totalProGearItems = myData.total_is_pro_gear_items
      ? myData.total_is_pro_gear_items
      : "0";
    const totalProGearWeight =
      myData.total_pro_gear_weight > 0
        ? myData.total_pro_gear_weight + " " + "Lbs."
        : "0";
    const totalItemExceptions = countException(myData.job_items);
    const totalItemFirearms = countfirearm(myData.job_items);
    const totalPads =
      myData.total_pads_used > 0 ? myData.total_pads_used + " " + "Lbs." : "0";
    let supervisorList = "";
    let workerList = "";

    if (myData.assign_worker.length > 0) {
      myData.assign_worker.map((worker) => {
        if (worker.role === "supervisor") {
          supervisorList = worker.first_name + " " + worker.last_name + " ";
        } else if (worker.role === "worker") {
          workerList = worker.first_name + " " + worker.last_name + " ";
        }
      });
    }
    shipmentDetails.addRow([
      myData.shipment_name,
      customerName,
      pickup_address,
      delivery_address,
      myData.total_items,
      totalCartons,
      totalVolume,
      totalWeight,
      totalDisassembledItems,
      totalElectronicsItems,
      totalHighValueItems,
      totalHighValue,
      totalProGearItems,
      totalProGearWeight,
      totalItemExceptions,
      totalPads,
      totalItemFirearms,
      supervisorList,
      workerList,
    ]);

    const itemList = workbook.addWorksheet("Item List");
    const itemListHeaders = [
      "Label",
      "Item Name",
      "Room",
      "Unit",
      "Location",
      "Unit Type",
      "Carton",
      "Electronics",
      "High Value",
      "Pro Gear",
      "Disassembled",
      "Tags",
      "Scanned-In Date/Time",
    ];
    itemList.addRow(itemListHeaders);
    const itemListHeaderRow = itemList.getRow(1);
    itemListHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    itemList.columns.forEach((column) => {
      column.width = 20;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        let checkLableNumber = "";
        if (
          item.isManualLabel === 1 ||
          item.isManualLabel === "true" ||
          item.isManualLabel === true
        ) {
          checkLableNumber =
            "M-" +
            item.color.charAt(0).toUpperCase() +
            "/" +
            item.lot_no +
            "/" +
            item.label_no;
        } else if (item.item_qr.type === "Generic") {
          checkLableNumber = "G" + item.item_qr.label_number;
        } else if (item.item_qr.type === "External") {
          checkLableNumber = "E" + item.item_qr.label_number;
        } else {
          checkLableNumber = item.item_qr.label_number;
        }

        let itemName = item.item_name.replace(/,/g, "");
        let roomName = item.room.name.replace(/,/g, "");

        let unitName = "No";
        if (item.unit_list && item.unit_list !== "") {
          unitName = item.unit_list.name;
        }

        let unitLocation = "No";
        if (item.unit_list && item.unit_list !== "") {
          unitLocation = item.unit_list.currentLocation;
        }

        let unitTypeName = "No";
        if (item.unit_list && item.unit_list !== "") {
          unitTypeName = item.unit_list.unitTypeName;
        }

        let isCartonCheck = "No";
        if (
          (item.is_carton === "YES" ||
            item.is_carton === 1 ||
            item.is_carton === "Yes" ||
            item.is_carton === "true" ||
            item.is_carton === "TRUE") &&
          (item.packed_by === "OWNER" || item.packed_by === "Packed by Owner")
        ) {
          isCartonCheck = "Y / PBO";
        } else if (
          (item.is_carton === "YES" ||
            item.is_carton === 1 ||
            item.is_carton === "Yes" ||
            item.is_carton === "true" ||
            item.is_carton === "TRUE") &&
          (item.packed_by === "MOVER" || item.packed_by === "Carrier Packed")
        ) {
          isCartonCheck = "Y /CP";
        } else {
          isCartonCheck = "No";
        }

        let isElectronicsCheck = "No";
        if (item.is_electronics === "YES" || item.is_electronics === "true") {
          isElectronicsCheck = "Y/" + item.serial_number;
        } else {
          isElectronicsCheck = "No";
        }

        let isCheckHighValue = "No";
        if (item.is_high_value == "true") {
          isCheckHighValue = "Y/" + "$" + item.declared_value;
        } else {
          isCheckHighValue = "No";
        }

        let isCheckProGearValue = "No";
        if (item.is_pro_gear === "true") {
          if (item.progear_name == "Member" || item.progear_name == "member") {
            isCheckProGearValue = "Y-M/" + item.pro_gear_weight + " lbs.";
          } else {
            isCheckProGearValue = "Y-S/" + item.pro_gear_weight + " lbs.";
          }
        } else {
          isCheckProGearValue = "No";
        }

        let isCheckDisassembled = "No";
        if (
          item.is_disassembled === "YES" ||
          item.is_disassembled === "true" ||
          item.is_disassembled === "TRUE"
        ) {
          if (
            (item.is_disassembled === "YES" ||
              item.is_disassembled === "true" ||
              item.is_disassembled === "TRUE") &&
            item.disassembled_by === "By Customer"
          ) {
            isCheckDisassembled = "Y / By Customer";
          } else {
            isCheckDisassembled = `Y / By Company`;
          }
        } else {
          isCheckDisassembled = "No";
        }

        let tagList = "";

        item.item_tag.forEach((tag, index) => {
          tagList += tag.dataValues.name;
          if (index !== item.item_tag.length - 1) {
            tagList += " ";
          }
        });

        const toDate = (date) => {
          let s = new Date(date)
            .toLocaleTimeString([], {
              day: "numeric",
              month: "short",
              year: "numeric",
              hour: "2-digit",
              minute: "2-digit",
              hour12: false,
              timeZone: "Asia/Kolkata",
            })
            .replace(/,/g, "");
          return String(s);
        };

        let createdDate = toDate(item.created_at);

        itemList.addRow([
          checkLableNumber,
          itemName,
          roomName,
          unitName,
          unitLocation,
          unitTypeName,
          isCartonCheck,
          isElectronicsCheck,
          isCheckHighValue,
          isCheckProGearValue,
          isCheckDisassembled,
          tagList,
          createdDate,
        ]);
      });
    }

    const itemListCP = workbook.addWorksheet("itemList CP");
    const itemListCPHeaders = [
      "Label",
      "Item Name",
      "Room",
      "Unit",
      "Location",
      "Unit Type",
      "Carton",
      "Electronics",
      "High Value",
      "Pro Gear",
      "Disassembled",
    ];
    itemListCP.addRow(itemListCPHeaders);
    const itemListCPHeaderRow = itemListCP.getRow(1);
    itemListCPHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    itemListCP.columns.forEach((column) => {
      column.width = 20;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        if (
          (item.is_carton === "YES" ||
            item.is_carton === 1 ||
            item.is_carton === "Yes" ||
            item.is_carton === "true" ||
            item.is_carton === "TRUE") &&
          item.packed_by_owner === 0
        ) {
          let checkLableNumber = "";
          if (
            item.isManualLabel === 1 ||
            item.isManualLabel === "true" ||
            item.isManualLabel === true
          ) {
            checkLableNumber =
              "M-" +
              item.color.charAt(0).toUpperCase() +
              "/" +
              item.lot_no +
              "/" +
              item.label_no;
          } else if (item.item_qr.type === "Generic") {
            checkLableNumber = "G" + item.item_qr.label_number;
          } else if (item.item_qr.type === "External") {
            checkLableNumber = "E" + item.item_qr.label_number;
          } else {
            checkLableNumber = item.item_qr.label_number;
          }

          let itemName = item.item_name.replace(/,/g, "");
          let roomName = item.room.name.replace(/,/g, "");

          let unitName = "No";
          if (item.unit_list && item.unit_list !== "") {
            unitName = item.unit_list.name;
          }

          let unitLocation = "No";
          if (item.unit_list && item.unit_list !== "") {
            unitLocation = item.unit_list.currentLocation;
          }

          let unitTypeName = "No";
          if (item.unit_list && item.unit_list !== "") {
            unitTypeName = item.unit_list.unitTypeName;
          }

          let isCartonCheck = "No";
          if (
            item.is_carton === "YES" ||
            item.is_carton === 1 ||
            item.is_carton === "Yes" ||
            item.is_carton === "true" ||
            item.is_carton === "TRUE"
          ) {
            isCartonCheck = "Yes";
          } else {
            isCartonCheck = "No";
          }

          let isElectronicsCheck = "No";
          if (item.is_electronics === "YES" || item.is_electronics === "true") {
            isElectronicsCheck = "Y/" + item.serial_number;
          } else {
            isElectronicsCheck = "No";
          }

          let isCheckHighValue = "No";
          if (item.is_high_value == "true") {
            isCheckHighValue = "Y/" + "$" + item.declared_value;
          } else {
            isCheckHighValue = "No";
          }

          let isCheckProGearValue = "No";
          if (item.is_pro_gear === "true") {
            if (
              item.progear_name == "Member" ||
              item.progear_name == "member"
            ) {
              isCheckProGearValue = "Y-M/" + item.pro_gear_weight + " lbs.";
            } else {
              isCheckProGearValue = "Y-S/" + item.pro_gear_weight + " lbs.";
            }
          } else {
            isCheckProGearValue = "No";
          }

          let isCheckDisassembled = "No";
          if (
            item.is_disassembled === "YES" ||
            item.is_disassembled === "true" ||
            item.is_disassembled === "TRUE"
          ) {
            if (
              (item.is_disassembled === "YES" ||
                item.is_disassembled === "true" ||
                item.is_disassembled === "TRUE") &&
              item.disassembled_by === "By Customer"
            ) {
              isCheckDisassembled = "Y / By Customer";
            } else {
              isCheckDisassembled = `Y / By Company`;
            }
          } else {
            isCheckDisassembled = "No";
          }

          itemListCP.addRow([
            checkLableNumber,
            itemName,
            roomName,
            unitName,
            unitLocation,
            unitTypeName,
            isCartonCheck,
            isElectronicsCheck,
            isCheckHighValue,
            isCheckProGearValue,
            isCheckDisassembled,
          ]);
        }
      });
    }

    const itemListPBO = workbook.addWorksheet("itemList PBO");
    const itemListPBOHeaders = [
      "Label",
      "Item Name",
      "Room",
      "Unit",
      "Location",
      "Unit Type",
      "Carton",
      "Electronics",
      "High Value",
      "Pro Gear",
      "Disassembled",
    ];
    itemListPBO.addRow(itemListPBOHeaders);
    const itemListPBOHeaderRow = itemListPBO.getRow(1);
    itemListPBOHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    itemListPBO.columns.forEach((column) => {
      column.width = 20;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        if (
          (item.is_carton === "YES" ||
            item.is_carton === 1 ||
            item.is_carton === "Yes" ||
            item.is_carton === "true" ||
            item.is_carton === "TRUE") &&
          item.packed_by_owner === 1
        ) {
          let checkLableNumber = "";
          if (
            item.isManualLabel === 1 ||
            item.isManualLabel === "true" ||
            item.isManualLabel === true
          ) {
            checkLableNumber =
              "M-" +
              item.color.charAt(0).toUpperCase() +
              "/" +
              item.lot_no +
              "/" +
              item.label_no;
          } else if (item.item_qr.type === "Generic") {
            checkLableNumber = "G" + item.item_qr.label_number;
          } else if (item.item_qr.type === "External") {
            checkLableNumber = "E" + item.item_qr.label_number;
          } else {
            checkLableNumber = item.item_qr.label_number;
          }

          let itemName = item.item_name.replace(/,/g, "");
          let roomName = item.room.name.replace(/,/g, "");

          let unitName = "No";
          if (item.unit_list && item.unit_list !== "") {
            unitName = item.unit_list.name;
          }

          let unitLocation = "No";
          if (item.unit_list && item.unit_list !== "") {
            unitLocation = item.unit_list.currentLocation;
          }

          let unitTypeName = "No";
          if (item.unit_list && item.unit_list !== "") {
            unitTypeName = item.unit_list.unitTypeName;
          }

          let isCartonCheck = "No";
          if (
            item.is_carton === "YES" ||
            item.is_carton === 1 ||
            item.is_carton === "Yes" ||
            item.is_carton === "true" ||
            item.is_carton === "TRUE"
          ) {
            isCartonCheck = "Yes";
          } else {
            isCartonCheck = "No";
          }

          let isElectronicsCheck = "No";
          if (item.is_electronics === "YES" || item.is_electronics === "true") {
            isElectronicsCheck = "Y/" + item.serial_number;
          } else {
            isElectronicsCheck = "No";
          }

          let isCheckHighValue = "No";
          if (item.is_high_value == "true") {
            isCheckHighValue = "Y/" + "$" + item.declared_value;
          } else {
            isCheckHighValue = "No";
          }

          let isCheckProGearValue = "No";
          if (item.is_pro_gear === "true") {
            if (
              item.progear_name == "Member" ||
              item.progear_name == "member"
            ) {
              isCheckProGearValue = "Y-M/" + item.pro_gear_weight + " lbs.";
            } else {
              isCheckProGearValue = "Y-S/" + item.pro_gear_weight + " lbs.";
            }
          } else {
            isCheckProGearValue = "No";
          }

          let isCheckDisassembled = "No";
          if (
            item.is_disassembled === "YES" ||
            item.is_disassembled === "true" ||
            item.is_disassembled === "TRUE"
          ) {
            if (
              (item.is_disassembled === "YES" ||
                item.is_disassembled === "true" ||
                item.is_disassembled === "TRUE") &&
              item.disassembled_by === "By Customer"
            ) {
              isCheckDisassembled = "Y / By Customer";
            } else {
              isCheckDisassembled = `Y / By Company`;
            }
          } else {
            isCheckDisassembled = "No";
          }

          itemListPBO.addRow([
            checkLableNumber,
            itemName,
            roomName,
            unitName,
            unitLocation,
            unitTypeName,
            isCartonCheck,
            isElectronicsCheck,
            isCheckHighValue,
            isCheckProGearValue,
            isCheckDisassembled,
          ]);
        }
      });
    }

    const exceptionList = workbook.addWorksheet("Exception List");
    const exceptionListHeaders = [
      "Label",
      "Item Name",
      "Exceptions",
      "Locations",
    ];
    exceptionList.addRow(exceptionListHeaders);
    const exceptionListHeaderRow = exceptionList.getRow(1);
    exceptionListHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    exceptionList.columns.forEach((column) => {
      column.width = 50;
    });

    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        let checkLableNumber = "";
        if (
          item.isManualLabel === 1 ||
          item.isManualLabel === "true" ||
          item.isManualLabel === true
        ) {
          checkLableNumber =
            "M-" +
            item.color.charAt(0).toUpperCase() +
            "/" +
            item.lot_no +
            "/" +
            item.label_no;
        } else if (item.item_qr.type === "Generic") {
          checkLableNumber = "G" + item.item_qr.label_number;
        } else if (item.item_qr.type === "External") {
          checkLableNumber = "E" + item.item_qr.label_number;
        } else {
          checkLableNumber = item.item_qr.label_number;
        }

        let itemName = item.item_name.replace(/,/g, "");
        let ExceptionsList = "";
        let LocationsList = "";

        if (item.exceptions.length > 0) {
          item.exceptions.map((e, i) => {
            {
              e.eid.length > 0
                ? e.eid.length == 1
                  ? (ExceptionsList = e.eid
                    .map((eid) => eid.dataValues.exception_name)
                    .join(" "))
                  : (ExceptionsList = e.eid
                    .map((eid) => eid.dataValues.exception_name)
                    .join(" "))
                : "-";
            }

            {
              e.lid.length > 0
                ? e.lid.length == 1
                  ? (LocationsList = e.lid
                    .map((lid) => lid.dataValues.location_name)
                    .join(" "))
                  : (LocationsList = e.lid
                    .map((lid) => lid.dataValues.location_name)
                    .join(" "))
                : "-";
            }
          });
        }
        exceptionList.addRow([
          checkLableNumber,
          itemName,
          ExceptionsList,
          LocationsList,
        ]);
      });
    }

    const proGearItemList = workbook.addWorksheet("Pro-Gear Items List");
    const proGearItemListHeaders = [
      "Label",
      "Item Name",
      "Room",
      "Pro Gear Weight",
    ];
    proGearItemList.addRow(proGearItemListHeaders);
    const proGearItemListHeaderRow = proGearItemList.getRow(1);
    proGearItemListHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    proGearItemList.columns.forEach((column) => {
      column.width = 22;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        if (item.is_pro_gear === "true") {
          let checkLableNumber = "";
          if (
            item.isManualLabel === 1 ||
            item.isManualLabel === "true" ||
            item.isManualLabel === true
          ) {
            checkLableNumber =
              "M-" +
              item.color.charAt(0).toUpperCase() +
              "/" +
              item.lot_no +
              "/" +
              item.label_no;
          } else if (item.item_qr.type === "Generic") {
            checkLableNumber = "G" + item.item_qr.label_number;
          } else if (item.item_qr.type === "External") {
            checkLableNumber = "E" + item.item_qr.label_number;
          } else {
            checkLableNumber = item.item_qr.label_number;
          }

          let itemName = item.item_name.replace(/,/g, "");
          let roomName = item.room.name.replace(/,/g, "");

          let isCheckProGearValue = "No";
          if (item.is_pro_gear === "true") {
            if (
              item.progear_name == "Member" ||
              item.progear_name == "member"
            ) {
              isCheckProGearValue = "Y-M/" + item.pro_gear_weight + " lbs.";
            } else {
              isCheckProGearValue = "Y-S/" + item.pro_gear_weight + " lbs.";
            }
          } else {
            isCheckProGearValue = "No";
          }

          proGearItemList.addRow([
            checkLableNumber,
            itemName,
            roomName,
            isCheckProGearValue,
          ]);
        }
      });
    }

    const highValueItemList = workbook.addWorksheet("High-Value Items List");
    const highValueItemListHeaders = [
      "Label",
      "Item Name",
      "Room",
      "Declared Value",
    ];
    highValueItemList.addRow(highValueItemListHeaders);
    const highValueItemListHeaderRow = highValueItemList.getRow(1);
    highValueItemListHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    highValueItemList.columns.forEach((column) => {
      column.width = 22;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        if (item.is_high_value == "true") {
          let checkLableNumber = "";
          if (
            item.isManualLabel === 1 ||
            item.isManualLabel === "true" ||
            item.isManualLabel === true
          ) {
            checkLableNumber =
              "M-" +
              item.color.charAt(0).toUpperCase() +
              "/" +
              item.lot_no +
              "/" +
              item.label_no;
          } else if (item.item_qr.type === "Generic") {
            checkLableNumber = "G" + item.item_qr.label_number;
          } else if (item.item_qr.type === "External") {
            checkLableNumber = "E" + item.item_qr.label_number;
          } else {
            checkLableNumber = item.item_qr.label_number;
          }

          let itemName = item.item_name.replace(/,/g, "");
          let roomName = item.room.name.replace(/,/g, "");

          let isCheckHighValue = "No";
          if (item.is_high_value == "true") {
            isCheckHighValue = "$" + " " + item.declared_value;
          } else {
            isCheckHighValue = "No";
          }

          highValueItemList.addRow([
            checkLableNumber,
            itemName,
            roomName,
            isCheckHighValue,
          ]);
        }
      });
    }

    const electronicsItemList = workbook.addWorksheet("Electronics Items List");
    const electronicsItemListHeaders = [
      "Label",
      "Item Name",
      "Room",
      "Electronics",
    ];
    electronicsItemList.addRow(electronicsItemListHeaders);
    const electronicsItemListHeaderRow = electronicsItemList.getRow(1);
    electronicsItemListHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    electronicsItemList.columns.forEach((column) => {
      column.width = 22;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        if (item.is_electronics === "YES" || item.is_electronics === "true") {
          let checkLableNumber = "";
          if (
            item.isManualLabel === 1 ||
            item.isManualLabel === "true" ||
            item.isManualLabel === true
          ) {
            checkLableNumber =
              "M-" +
              item.color.charAt(0).toUpperCase() +
              "/" +
              item.lot_no +
              "/" +
              item.label_no;
          } else if (item.item_qr.type === "Generic") {
            checkLableNumber = "G" + item.item_qr.label_number;
          } else if (item.item_qr.type === "External") {
            checkLableNumber = "E" + item.item_qr.label_number;
          } else {
            checkLableNumber = item.item_qr.label_number;
          }

          let itemName = item.item_name.replace(/,/g, "");
          let roomName = item.room.name.replace(/,/g, "");

          let isElectronicsCheck = "No";
          if (item.is_electronics === "YES" || item.is_electronics === "true") {
            isElectronicsCheck = "Y/" + item.serial_number;
          } else {
            isElectronicsCheck = "No";
          }

          electronicsItemList.addRow([
            checkLableNumber,
            itemName,
            roomName,
            isElectronicsCheck,
          ]);
        }
      });
    }

    const firearmsItemList = workbook.addWorksheet("Firearms Items List");
    const firearmsItemListHeaders = ["Label", "Item Name", "Room", "Serial No"];
    firearmsItemList.addRow(firearmsItemListHeaders);
    const firearmsItemListHeaderRow = firearmsItemList.getRow(1);
    firearmsItemListHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    firearmsItemList.columns.forEach((column) => {
      column.width = 22;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        if (item.is_firearm === "true" || item.is_firearm === "YES") {
          let checkLableNumber = "";
          if (
            item.isManualLabel === 1 ||
            item.isManualLabel === "true" ||
            item.isManualLabel === true
          ) {
            checkLableNumber =
              "M-" +
              item.color.charAt(0).toUpperCase() +
              "/" +
              item.lot_no +
              "/" +
              item.label_no;
          } else if (item.item_qr.type === "Generic") {
            checkLableNumber = "G" + item.item_qr.label_number;
          } else if (item.item_qr.type === "External") {
            checkLableNumber = "E" + item.item_qr.label_number;
          } else {
            checkLableNumber = item.item_qr.label_number;
          }

          let itemName = item.item_name.replace(/,/g, "");
          let roomName = item.room.name.replace(/,/g, "");

          let isFirearmCheck = "No";
          if (item.is_firearm === "true" || item.is_firearm === "YES") {
            isFirearmCheck = item.firmarm_serial_number;
          } else {
            isFirearmCheck = "No";
          }

          firearmsItemList.addRow([
            checkLableNumber,
            itemName,
            roomName,
            isFirearmCheck,
          ]);
        }
      });
    }

    const SignatureHistory = workbook.addWorksheet("Signature History");
    const SignatureHistoryHeaders = [
      "Stage",
      "User Name",
      "User Notes",
      "Customer Name",
      "Customer Notes",
      "Date/Time",
    ];
    SignatureHistory.addRow(SignatureHistoryHeaders);
    const SignatureHistoryHeaderRow = SignatureHistory.getRow(1);
    SignatureHistoryHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    SignatureHistory.columns.forEach((column) => {
      column.width = 22;
    });

    if (myData.shipment_type_for_shipment.local_shipment_stage.length > 0) {
      myData.shipment_type_for_shipment.local_shipment_stage.forEach(
        (Stage, index) => {
          const userName = Stage.supervisor_signature
            ? Stage.supervisor_name
            : "";
          const whySupervisorSignatureRequireNote = Stage.supervisor_signature
            ? Stage.why_supervisor_signature_require_note
            : "";

          const customerName = Stage.customer_signature
            ? Stage.customer_name
            : "";
          const whyCustomerSignatureRequireNote = Stage.customer_signature
            ? Stage.why_customer_signature_require_note
            : "";
          const date = toDate(Stage.created_at, Stage.PDF_time_require);

          SignatureHistory.addRow([
            Stage.name,
            userName,
            whySupervisorSignatureRequireNote,
            customerName,
            whyCustomerSignatureRequireNote,
            date,
          ]);
        }
      );
    }

    const excelFilePath = path.join(
      __dirname,
      "../../temp",
      `${shipmentName}.xlsx`
    ); // Change the file path as needed
    await workbook.xlsx.writeFile(excelFilePath);

    const csvFilePathMerged = path.join(
      __dirname,
      "../../temp",
      `${shipmentName}.csv`
    ); // Change the file path as needed

    await workbook.xlsx
      .readFile(excelFilePath)
      .then(() => {
        const csvStream = fs.createWriteStream(csvFilePathMerged);

        // Iterate through each worksheet and convert it to CSV
        workbook.eachSheet((worksheet, sheetId) => {
          csvStream.write(`\n\n ${worksheet.name}\n`);
          worksheet.columns.forEach((column) => {
            column.width = 25; // Adjust the width as needed
          });
          const headerRow = worksheet.getRow(2);
          headerRow.eachCell((cell) => {
            cell.font = { bold: true };
          });
          worksheet.eachRow((row, rowNumber) => {
            const rowValues = row.values
              .map((cell) => cell.toString())
              .join(",");
            csvStream.write(rowValues + "\n");
          });
        });
        csvStream.end();
      })
      .catch((error) => {
        console.log("exports.IterateExcelFileError -> error: ", error);
      });
    generateResponse(
      response,
      SUCCESS_CODE,
      1,
      "Success",
      `temp/${shipmentName}.csv`
    );
  } catch (error) {
    console.log(
      "exports.csvExportShipmentInventoryController -> error: ",
      error
    );
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.xlsxExportShipmentInventoryController = async (request, response) => {
  try {
    const { shipmentId } = request.body;
    const myData = await shipmentModel.getShipmentDetailModelForPdf(shipmentId);
    const inventoryData = await shipmentModel.getInventoryListForPDF(
      shipmentId
    );
    const shipmentName = `${myData.job_number}-${myData.shipment_name
      .replace(/[#]/g, "")
      .replace(/\.\./g, "")}`;

    let sortArray = inventoryData;
    let newArray = await sortArray.sort(function (a, b) {
      const nameA = a.item_qr.type.toUpperCase();
      const nameB = b.item_qr.type.toUpperCase();
      if (nameA > nameB) {
        return -1;
      }
      if (nameA < nameB) {
        return 1;
      }
      return 0;
    });
    let aginNewArray = await sortArray.sort(function (a, b) {
      const nameA = a.isManualLabel;
      const nameB = b.isManualLabel;
      if (nameA > nameB) {
        return 1;
      }
      if (nameA < nameB) {
        return -1;
      }
      return 0;
    });

    myData.job_items = await [];
    myData.job_items = await [...aginNewArray];

    const headerFont = {
      name: "Arial",
      bold: true,
      size: 12,
    };

    const workbook = new ExcelJS.Workbook();
    const shipmentDetails = workbook.addWorksheet("Shipment Details");
    const shipmentDetailsHeaders = [
      "Shipment Name",
      "Customer Name",
      "Origin Address",
      "Destination Address",
      "Total Items",
      "Total Cartons",
      "Total Volume",
      "Total Weight",
      "Disassembled Items",
      "Electronics",
      "High Value Items",
      "High Value Total",
      "Pro Gear Items",
      "Pro Gear Weight",
      "Items With Exceptions",
      "Pads Used",
      "Firearms Total Qty",
      "Supervisor",
      "Worker",
    ];

    shipmentDetails.addRow(shipmentDetailsHeaders);
    const shipmentDetailsHeaderRow = shipmentDetails.getRow(1);
    shipmentDetailsHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    shipmentDetails.columns.forEach((column) => {
      column.width = 20;
    });

    const customerName =
      myData.customer_job.first_name + "," + myData.customer_job.last_name;
    const pickup_address1 =
      myData.pickup_address !== undefined &&
        myData.pickup_address !== null &&
        myData.pickup_address !== ""
        ? myData.pickup_address
        : "";
    const pickup_address2 =
      myData.pickup_address2 !== undefined &&
        myData.pickup_address2 !== null &&
        myData.pickup_address2 !== ""
        ? "," + myData.pickup_address2
        : "";
    const pickup_city =
      myData.pickup_city !== undefined &&
        myData.pickup_city !== null &&
        myData.pickup_city !== ""
        ? "," + myData.pickup_city
        : "";
    const pickup_state =
      myData.pickup_state !== undefined &&
        myData.pickup_city !== null &&
        myData.pickup_city !== ""
        ? "," + myData.pickup_state
        : "";
    const pickup_country =
      myData.pickup_country !== undefined &&
        myData.pickup_country !== null &&
        myData.pickup_country !== ""
        ? "," + myData.pickup_country
        : "";
    const pickup_zipcode =
      myData.pickup_zipcode !== undefined &&
        myData.pickup_zipcode !== null &&
        myData.pickup_zipcode !== ""
        ? "," + myData.pickup_zipcode
        : "";
    const pickup_address =
      pickup_address1 +
      pickup_address2 +
      pickup_city +
      pickup_state +
      pickup_country +
      pickup_zipcode;

    const delivery_address1 =
      myData.delivery_address !== undefined &&
        myData.delivery_address !== null &&
        myData.delivery_address !== ""
        ? myData.delivery_address
        : "";
    const delivery_address2 =
      myData.delivery_address2 !== undefined &&
        myData.delivery_address2 !== null &&
        myData.delivery_address2 !== ""
        ? "," + myData.delivery_address2
        : "";
    const delivery_city =
      myData.delivery_city !== undefined &&
        myData.delivery_city !== null &&
        myData.delivery_city !== ""
        ? "," + myData.delivery_city
        : "";
    const delivery_state =
      myData.delivery_state !== undefined &&
        myData.delivery_state !== null &&
        myData.delivery_state !== ""
        ? "," + myData.delivery_state
        : "";
    const delivery_country =
      myData.delivery_country !== undefined &&
        myData.delivery_country !== null &&
        myData.delivery_country !== ""
        ? "," + myData.delivery_country
        : "";
    const delivery_zipcode =
      myData.delivery_zipcode !== undefined &&
        myData.delivery_zipcode !== null &&
        myData.delivery_zipcode !== ""
        ? "," + myData.delivery_zipcode
        : "";
    const delivery_address =
      delivery_address1 +
      delivery_address2 +
      delivery_city +
      delivery_state +
      delivery_country +
      delivery_zipcode;

    const totalCartons = `${myData.total_cartons} (CP ${myData.total_cartons_cp} PBO ${myData.total_cartons_pbo})`;
    const totalVolume = Math.round(myData.total_volume) + " " + "Cu Ft.";
    const totalWeight = myData.total_weight
      ? myData.total_weight + " " + "Lbs."
      : "0";
    const totalDisassembledItems =
      myData.total_disassembled_items > 0
        ? myData.total_disassembled_items
        : "0";
    const totalElectronicsItems = myData.total_electronics_items
      ? myData.total_electronics_items
      : "0";
    const totalHighValueItems = myData.total_highValue_items
      ? myData.total_highValue_items
      : "0";
    const totalHighValue = myData.total_high_value
      ? "$" + " " + myData.total_high_value
      : "0";
    const totalProGearItems = myData.total_is_pro_gear_items
      ? myData.total_is_pro_gear_items
      : "0";
    const totalProGearWeight =
      myData.total_pro_gear_weight > 0
        ? myData.total_pro_gear_weight + " " + "Lbs."
        : "0";
    const totalItemExceptions = countException(myData.job_items);
    const totalItemFirearms = countfirearm(myData.job_items);
    const totalPads =
      myData.total_pads_used > 0 ? myData.total_pads_used + " " + "Lbs." : "0";
    let supervisorList = "";
    let workerList = "";

    if (myData.assign_worker.length > 0) {
      myData.assign_worker.map((worker) => {
        if (worker.role === "supervisor") {
          supervisorList =
            worker.first_name + " " + worker.last_name + "," + " ";
        } else if (worker.role === "worker") {
          workerList = worker.first_name + " " + worker.last_name + "," + " ";
        }
      });
    }
    shipmentDetails.addRow([
      myData.shipment_name,
      customerName,
      pickup_address,
      delivery_address,
      myData.total_items,
      totalCartons,
      totalVolume,
      totalWeight,
      totalDisassembledItems,
      totalElectronicsItems,
      totalHighValueItems,
      totalHighValue,
      totalProGearItems,
      totalProGearWeight,
      totalItemExceptions,
      totalPads,
      totalItemFirearms,
      supervisorList,
      workerList,
    ]);

    const itemList = workbook.addWorksheet("Item List");
    const itemListHeaders = [
      "Label",
      "Item Name",
      "Room",
      "Unit",
      "Location",
      "Unit Type",
      "Carton",
      "Electronics",
      "High Value",
      "Pro Gear",
      "Disassembled",
      "Tags",
      "Scanned-In Date/Time",
    ];
    itemList.addRow(itemListHeaders);
    const itemListHeaderRow = itemList.getRow(1);
    itemListHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    itemList.columns.forEach((column) => {
      column.width = 20;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        let checkLableNumber = "";
        if (
          item.isManualLabel === 1 ||
          item.isManualLabel === "true" ||
          item.isManualLabel === true
        ) {
          checkLableNumber =
            "M-" +
            item.color.charAt(0).toUpperCase() +
            "/" +
            item.lot_no +
            "/" +
            item.label_no;
        } else if (item.item_qr.type === "Generic") {
          checkLableNumber = "G" + item.item_qr.label_number;
        } else if (item.item_qr.type === "External") {
          checkLableNumber = "E" + item.item_qr.label_number;
        } else {
          checkLableNumber = item.item_qr.label_number;
        }

        let itemName = item.item_name;
        let roomName = item.room.name;

        let unitName = "No";
        if (item.unit_list && item.unit_list !== "") {
          unitName = item.unit_list.name;
        }

        let unitLocation = "No";
        if (item.unit_list && item.unit_list !== "") {
          unitLocation = item.unit_list.currentLocation;
        }

        let unitTypeName = "No";
        if (item.unit_list && item.unit_list !== "") {
          unitTypeName = item.unit_list.unitTypeName;
        }

        let isCartonCheck = "No";
        if (
          (item.is_carton === "YES" ||
            item.is_carton === 1 ||
            item.is_carton === "Yes" ||
            item.is_carton === "true" ||
            item.is_carton === "TRUE") &&
          (item.packed_by === "OWNER" || item.packed_by === "Packed by Owner")
        ) {
          isCartonCheck = "Y / PBO";
        } else if (
          (item.is_carton === "YES" ||
            item.is_carton === 1 ||
            item.is_carton === "Yes" ||
            item.is_carton === "true" ||
            item.is_carton === "TRUE") &&
          (item.packed_by === "MOVER" || item.packed_by === "Carrier Packed")
        ) {
          isCartonCheck = "Y /CP";
        } else {
          isCartonCheck = "No";
        }

        let isElectronicsCheck = "No";
        if (item.is_electronics === "YES" || item.is_electronics === "true") {
          isElectronicsCheck = "Y/" + item.serial_number;
        } else {
          isElectronicsCheck = "No";
        }

        let isCheckHighValue = "No";
        if (item.is_high_value == "true") {
          isCheckHighValue = "Y/" + "$" + item.declared_value;
        } else {
          isCheckHighValue = "No";
        }

        let isCheckProGearValue = "No";
        if (item.is_pro_gear === "true") {
          if (item.progear_name == "Member" || item.progear_name == "member") {
            isCheckProGearValue = "Y-M/" + item.pro_gear_weight + " lbs.";
          } else {
            isCheckProGearValue = "Y-S/" + item.pro_gear_weight + " lbs.";
          }
        } else {
          isCheckProGearValue = "No";
        }

        let isCheckDisassembled = "No";
        if (
          item.is_disassembled === "YES" ||
          item.is_disassembled === "true" ||
          item.is_disassembled === "TRUE"
        ) {
          if (
            (item.is_disassembled === "YES" ||
              item.is_disassembled === "true" ||
              item.is_disassembled === "TRUE") &&
            item.disassembled_by === "By Customer"
          ) {
            isCheckDisassembled = "Y / By Customer";
          } else {
            isCheckDisassembled = `Y / By Company`;
          }
        } else {
          isCheckDisassembled = "No";
        }

        let tagList = "";

        item.item_tag.forEach((tag, index) => {
          tagList += tag.dataValues.name;
          if (index !== item.item_tag.length - 1) {
            tagList += ", ";
          }
        });

        const toDate = (date) => {
          let s = new Date(date).toLocaleTimeString([], {
            day: "numeric",
            month: "short",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
            timeZone: "Asia/Kolkata",
          });
          return s;
        };

        let createdDate = toDate(item.created_at);

        itemList.addRow([
          checkLableNumber,
          itemName,
          roomName,
          unitName,
          unitLocation,
          unitTypeName,
          isCartonCheck,
          isElectronicsCheck,
          isCheckHighValue,
          isCheckProGearValue,
          isCheckDisassembled,
          tagList,
          createdDate,
        ]);
      });
    }

    const itemListCP = workbook.addWorksheet("itemList CP");
    const itemListCPHeaders = [
      "Label",
      "Item Name",
      "Room",
      "Unit",
      "Location",
      "Unit Type",
      "Carton",
      "Electronics",
      "High Value",
      "Pro Gear",
      "Disassembled",
    ];
    itemListCP.addRow(itemListCPHeaders);
    const itemListCPHeaderRow = itemListCP.getRow(1);
    itemListCPHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    itemListCP.columns.forEach((column) => {
      column.width = 20;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        if (
          (item.is_carton === "YES" ||
            item.is_carton === 1 ||
            item.is_carton === "Yes" ||
            item.is_carton === "true" ||
            item.is_carton === "TRUE") &&
          item.packed_by_owner === 0
        ) {
          let checkLableNumber = "";
          if (
            item.isManualLabel === 1 ||
            item.isManualLabel === "true" ||
            item.isManualLabel === true
          ) {
            checkLableNumber =
              "M-" +
              item.color.charAt(0).toUpperCase() +
              "/" +
              item.lot_no +
              "/" +
              item.label_no;
          } else if (item.item_qr.type === "Generic") {
            checkLableNumber = "G" + item.item_qr.label_number;
          } else if (item.item_qr.type === "External") {
            checkLableNumber = "E" + item.item_qr.label_number;
          } else {
            checkLableNumber = item.item_qr.label_number;
          }

          let itemName = item.item_name;
          let roomName = item.room.name;

          let unitName = "No";
          if (item.unit_list && item.unit_list !== "") {
            unitName = item.unit_list.name;
          }

          let unitLocation = "No";
          if (item.unit_list && item.unit_list !== "") {
            unitLocation = item.unit_list.currentLocation;
          }

          let unitTypeName = "No";
          if (item.unit_list && item.unit_list !== "") {
            unitTypeName = item.unit_list.unitTypeName;
          }

          let isCartonCheck = "No";
          if (
            item.is_carton === "YES" ||
            item.is_carton === 1 ||
            item.is_carton === "Yes" ||
            item.is_carton === "true" ||
            item.is_carton === "TRUE"
          ) {
            isCartonCheck = "Yes";
          } else {
            isCartonCheck = "No";
          }

          let isElectronicsCheck = "No";
          if (item.is_electronics === "YES" || item.is_electronics === "true") {
            isElectronicsCheck = "Y/" + item.serial_number;
          } else {
            isElectronicsCheck = "No";
          }

          let isCheckHighValue = "No";
          if (item.is_high_value == "true") {
            isCheckHighValue = "Y/" + "$" + item.declared_value;
          } else {
            isCheckHighValue = "No";
          }

          let isCheckProGearValue = "No";
          if (item.is_pro_gear === "true") {
            if (
              item.progear_name == "Member" ||
              item.progear_name == "member"
            ) {
              isCheckProGearValue = "Y-M/" + item.pro_gear_weight + " lbs.";
            } else {
              isCheckProGearValue = "Y-S/" + item.pro_gear_weight + " lbs.";
            }
          } else {
            isCheckProGearValue = "No";
          }

          let isCheckDisassembled = "No";
          if (
            item.is_disassembled === "YES" ||
            item.is_disassembled === "true" ||
            item.is_disassembled === "TRUE"
          ) {
            if (
              (item.is_disassembled === "YES" ||
                item.is_disassembled === "true" ||
                item.is_disassembled === "TRUE") &&
              item.disassembled_by === "By Customer"
            ) {
              isCheckDisassembled = "Y / By Customer";
            } else {
              isCheckDisassembled = `Y / By Company`;
            }
          } else {
            isCheckDisassembled = "No";
          }

          itemListCP.addRow([
            checkLableNumber,
            itemName,
            roomName,
            unitName,
            unitLocation,
            unitTypeName,
            isCartonCheck,
            isElectronicsCheck,
            isCheckHighValue,
            isCheckProGearValue,
            isCheckDisassembled,
          ]);
        }
      });
    }

    const itemListPBO = workbook.addWorksheet("itemList PBO");
    const itemListPBOHeaders = [
      "Label",
      "Item Name",
      "Room",
      "Unit",
      "Location",
      "Unit Type",
      "Carton",
      "Electronics",
      "High Value",
      "Pro Gear",
      "Disassembled",
    ];
    itemListPBO.addRow(itemListPBOHeaders);
    const itemListPBOHeaderRow = itemListPBO.getRow(1);
    itemListPBOHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    itemListPBO.columns.forEach((column) => {
      column.width = 20;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        if (
          (item.is_carton === "YES" ||
            item.is_carton === 1 ||
            item.is_carton === "Yes" ||
            item.is_carton === "true" ||
            item.is_carton === "TRUE") &&
          item.packed_by_owner === 1
        ) {
          let checkLableNumber = "";
          if (
            item.isManualLabel === 1 ||
            item.isManualLabel === "true" ||
            item.isManualLabel === true
          ) {
            checkLableNumber =
              "M-" +
              item.color.charAt(0).toUpperCase() +
              "/" +
              item.lot_no +
              "/" +
              item.label_no;
          } else if (item.item_qr.type === "Generic") {
            checkLableNumber = "G" + item.item_qr.label_number;
          } else if (item.item_qr.type === "External") {
            checkLableNumber = "E" + item.item_qr.label_number;
          } else {
            checkLableNumber = item.item_qr.label_number;
          }

          let itemName = item.item_name;
          let roomName = item.room.name;

          let unitName = "No";
          if (item.unit_list && item.unit_list !== "") {
            unitName = item.unit_list.name;
          }

          let unitLocation = "No";
          if (item.unit_list && item.unit_list !== "") {
            unitLocation = item.unit_list.currentLocation;
          }

          let unitTypeName = "No";
          if (item.unit_list && item.unit_list !== "") {
            unitTypeName = item.unit_list.unitTypeName;
          }

          let isCartonCheck = "No";
          if (
            item.is_carton === "YES" ||
            item.is_carton === 1 ||
            item.is_carton === "Yes" ||
            item.is_carton === "true" ||
            item.is_carton === "TRUE"
          ) {
            isCartonCheck = "Yes";
          } else {
            isCartonCheck = "No";
          }

          let isElectronicsCheck = "No";
          if (item.is_electronics === "YES" || item.is_electronics === "true") {
            isElectronicsCheck = "Y/" + item.serial_number;
          } else {
            isElectronicsCheck = "No";
          }

          let isCheckHighValue = "No";
          if (item.is_high_value == "true") {
            isCheckHighValue = "Y/" + "$" + item.declared_value;
          } else {
            isCheckHighValue = "No";
          }

          let isCheckProGearValue = "No";
          if (item.is_pro_gear === "true") {
            if (
              item.progear_name == "Member" ||
              item.progear_name == "member"
            ) {
              isCheckProGearValue = "Y-M/" + item.pro_gear_weight + " lbs.";
            } else {
              isCheckProGearValue = "Y-S/" + item.pro_gear_weight + " lbs.";
            }
          } else {
            isCheckProGearValue = "No";
          }

          let isCheckDisassembled = "No";
          if (
            item.is_disassembled === "YES" ||
            item.is_disassembled === "true" ||
            item.is_disassembled === "TRUE"
          ) {
            if (
              (item.is_disassembled === "YES" ||
                item.is_disassembled === "true" ||
                item.is_disassembled === "TRUE") &&
              item.disassembled_by === "By Customer"
            ) {
              isCheckDisassembled = "Y / By Customer";
            } else {
              isCheckDisassembled = `Y / By Company`;
            }
          } else {
            isCheckDisassembled = "No";
          }

          itemListPBO.addRow([
            checkLableNumber,
            itemName,
            roomName,
            unitName,
            unitLocation,
            unitTypeName,
            isCartonCheck,
            isElectronicsCheck,
            isCheckHighValue,
            isCheckProGearValue,
            isCheckDisassembled,
          ]);
        }
      });
    }

    const exceptionList = workbook.addWorksheet("Exception List");
    const exceptionListHeaders = [
      "Label",
      "Item Name",
      "Exceptions",
      "Locations",
    ];
    exceptionList.addRow(exceptionListHeaders);
    const exceptionListHeaderRow = exceptionList.getRow(1);
    exceptionListHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    exceptionList.columns.forEach((column) => {
      column.width = 50;
    });

    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        let checkLableNumber = "";
        if (
          item.isManualLabel === 1 ||
          item.isManualLabel === "true" ||
          item.isManualLabel === true
        ) {
          checkLableNumber =
            "M-" +
            item.color.charAt(0).toUpperCase() +
            "/" +
            item.lot_no +
            "/" +
            item.label_no;
        } else if (item.item_qr.type === "Generic") {
          checkLableNumber = "G" + item.item_qr.label_number;
        } else if (item.item_qr.type === "External") {
          checkLableNumber = "E" + item.item_qr.label_number;
        } else {
          checkLableNumber = item.item_qr.label_number;
        }

        let itemName = item.item_name;
        let ExceptionsList = "";
        let LocationsList = "";

        if (item.exceptions.length > 0) {
          item.exceptions.map((e, i) => {
            {
              e.eid.length > 0
                ? e.eid.length == 1
                  ? (ExceptionsList = e.eid
                    .map((eid) => eid.dataValues.exception_name)
                    .join(","))
                  : (ExceptionsList = e.eid
                    .map((eid) => eid.dataValues.exception_name)
                    .join(","))
                : "-";
            }

            {
              e.lid.length > 0
                ? e.lid.length == 1
                  ? (LocationsList = e.lid
                    .map((lid) => lid.dataValues.location_name)
                    .join(","))
                  : (LocationsList = e.lid
                    .map((lid) => lid.dataValues.location_name)
                    .join(","))
                : "-";
            }
          });
        }
        exceptionList.addRow([
          checkLableNumber,
          itemName,
          ExceptionsList,
          LocationsList,
        ]);
      });
    }

    const proGearItemList = workbook.addWorksheet("Pro-Gear Items List");
    const proGearItemListHeaders = [
      "Label",
      "Item Name",
      "Room",
      "Pro Gear Weight",
    ];
    proGearItemList.addRow(proGearItemListHeaders);
    const proGearItemListHeaderRow = proGearItemList.getRow(1);
    proGearItemListHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    proGearItemList.columns.forEach((column) => {
      column.width = 22;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        if (item.is_pro_gear === "true") {
          let checkLableNumber = "";
          if (
            item.isManualLabel === 1 ||
            item.isManualLabel === "true" ||
            item.isManualLabel === true
          ) {
            checkLableNumber =
              "M-" +
              item.color.charAt(0).toUpperCase() +
              "/" +
              item.lot_no +
              "/" +
              item.label_no;
          } else if (item.item_qr.type === "Generic") {
            checkLableNumber = "G" + item.item_qr.label_number;
          } else if (item.item_qr.type === "External") {
            checkLableNumber = "E" + item.item_qr.label_number;
          } else {
            checkLableNumber = item.item_qr.label_number;
          }

          let itemName = item.item_name;
          let roomName = item.room.name;

          let isCheckProGearValue = "No";
          if (item.is_pro_gear === "true") {
            if (
              item.progear_name == "Member" ||
              item.progear_name == "member"
            ) {
              isCheckProGearValue = "Y-M/" + item.pro_gear_weight + " lbs.";
            } else {
              isCheckProGearValue = "Y-S/" + item.pro_gear_weight + " lbs.";
            }
          } else {
            isCheckProGearValue = "No";
          }

          proGearItemList.addRow([
            checkLableNumber,
            itemName,
            roomName,
            isCheckProGearValue,
          ]);
        }
      });
    }

    const highValueItemList = workbook.addWorksheet("High-Value Items List");
    const highValueItemListHeaders = [
      "Label",
      "Item Name",
      "Room",
      "Declared Value",
    ];
    highValueItemList.addRow(highValueItemListHeaders);
    const highValueItemListHeaderRow = highValueItemList.getRow(1);
    highValueItemListHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    highValueItemList.columns.forEach((column) => {
      column.width = 22;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        if (item.is_high_value == "true") {
          let checkLableNumber = "";
          if (
            item.isManualLabel === 1 ||
            item.isManualLabel === "true" ||
            item.isManualLabel === true
          ) {
            checkLableNumber =
              "M-" +
              item.color.charAt(0).toUpperCase() +
              "/" +
              item.lot_no +
              "/" +
              item.label_no;
          } else if (item.item_qr.type === "Generic") {
            checkLableNumber = "G" + item.item_qr.label_number;
          } else if (item.item_qr.type === "External") {
            checkLableNumber = "E" + item.item_qr.label_number;
          } else {
            checkLableNumber = item.item_qr.label_number;
          }

          let itemName = item.item_name;
          let roomName = item.room.name;

          let isCheckHighValue = "No";
          if (item.is_high_value == "true") {
            isCheckHighValue = "$" + " " + item.declared_value;
          } else {
            isCheckHighValue = "No";
          }

          highValueItemList.addRow([
            checkLableNumber,
            itemName,
            roomName,
            isCheckHighValue,
          ]);
        }
      });
    }

    const electronicsItemList = workbook.addWorksheet("Electronics Items List");
    const electronicsItemListHeaders = [
      "Label",
      "Item Name",
      "Room",
      "Electronics",
    ];
    electronicsItemList.addRow(electronicsItemListHeaders);
    const electronicsItemListHeaderRow = electronicsItemList.getRow(1);
    electronicsItemListHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    electronicsItemList.columns.forEach((column) => {
      column.width = 22;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        if (item.is_electronics === "YES" || item.is_electronics === "true") {
          let checkLableNumber = "";
          if (
            item.isManualLabel === 1 ||
            item.isManualLabel === "true" ||
            item.isManualLabel === true
          ) {
            checkLableNumber =
              "M-" +
              item.color.charAt(0).toUpperCase() +
              "/" +
              item.lot_no +
              "/" +
              item.label_no;
          } else if (item.item_qr.type === "Generic") {
            checkLableNumber = "G" + item.item_qr.label_number;
          } else if (item.item_qr.type === "External") {
            checkLableNumber = "E" + item.item_qr.label_number;
          } else {
            checkLableNumber = item.item_qr.label_number;
          }

          let itemName = item.item_name;
          let roomName = item.room.name;

          let isElectronicsCheck = "No";
          if (item.is_electronics === "YES" || item.is_electronics === "true") {
            isElectronicsCheck = "Y/" + item.serial_number;
          } else {
            isElectronicsCheck = "No";
          }

          electronicsItemList.addRow([
            checkLableNumber,
            itemName,
            roomName,
            isElectronicsCheck,
          ]);
        }
      });
    }

    const firearmsItemList = workbook.addWorksheet("Firearms Items List");
    const firearmsItemListHeaders = ["Label", "Item Name", "Room", "Serial No"];
    firearmsItemList.addRow(firearmsItemListHeaders);
    const firearmsItemListHeaderRow = firearmsItemList.getRow(1);
    firearmsItemListHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    firearmsItemList.columns.forEach((column) => {
      column.width = 22;
    });
    if (myData.job_items.length > 0) {
      myData.job_items.forEach((item, index) => {
        if (item.is_firearm === "true" || item.is_firearm === "YES") {
          let checkLableNumber = "";
          if (
            item.isManualLabel === 1 ||
            item.isManualLabel === "true" ||
            item.isManualLabel === true
          ) {
            checkLableNumber =
              "M-" +
              item.color.charAt(0).toUpperCase() +
              "/" +
              item.lot_no +
              "/" +
              item.label_no;
          } else if (item.item_qr.type === "Generic") {
            checkLableNumber = "G" + item.item_qr.label_number;
          } else if (item.item_qr.type === "External") {
            checkLableNumber = "E" + item.item_qr.label_number;
          } else {
            checkLableNumber = item.item_qr.label_number;
          }

          let itemName = item.item_name;
          let roomName = item.room.name;

          let isFirearmCheck = "No";
          if (item.is_firearm === "true" || item.is_firearm === "YES") {
            isFirearmCheck = item.firmarm_serial_number;
          } else {
            isFirearmCheck = "No";
          }

          firearmsItemList.addRow([
            checkLableNumber,
            itemName,
            roomName,
            isFirearmCheck,
          ]);
        }
      });
    }

    const SignatureHistory = workbook.addWorksheet("Signature History");
    const SignatureHistoryHeaders = [
      "Stage",
      "User Name",
      "User Notes",
      "Customer Name",
      "Customer Notes",
      "Date/Time",
    ];
    SignatureHistory.addRow(SignatureHistoryHeaders);
    const SignatureHistoryHeaderRow = SignatureHistory.getRow(1);
    SignatureHistoryHeaderRow.eachCell((cell) => {
      cell.font = headerFont;
    });
    SignatureHistory.columns.forEach((column) => {
      column.width = 22;
    });

    if (myData.shipment_type_for_shipment.local_shipment_stage.length > 0) {
      myData.shipment_type_for_shipment.local_shipment_stage.forEach(
        (Stage, index) => {
          const userName = Stage.supervisor_signature
            ? Stage.supervisor_name
            : "";
          const whySupervisorSignatureRequireNote = Stage.supervisor_signature
            ? Stage.why_supervisor_signature_require_note
            : "";

          const customerName = Stage.customer_signature
            ? Stage.customer_name
            : "";
          const whyCustomerSignatureRequireNote = Stage.customer_signature
            ? Stage.why_customer_signature_require_note
            : "";
          const date = toDate(Stage.created_at, Stage.PDF_time_require);

          SignatureHistory.addRow([
            Stage.name,
            userName,
            whySupervisorSignatureRequireNote,
            customerName,
            whyCustomerSignatureRequireNote,
            date,
          ]);
        }
      );
    }
    const excelFilePath = path.join(
      __dirname,
      "../../temp",
      `${shipmentName}.xlsx`
    ); // Change the file path as needed
    await workbook.xlsx.writeFile(excelFilePath);
    generateResponse(
      response,
      SUCCESS_CODE,
      1,
      "Success",
      `temp/${shipmentName}.xlsx`
    );
  } catch (error) {
    console.log(
      "exports.csvExportShipmentInventoryController -> error: ",
      error
    );
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.downloadPdfController = async (request, response) => {
  try {
    request.connection.setTimeout(1000000000000000);
    const { shipmentId, isPrintPhotos, isDownloadByCustomer } = request.body;

    const [myData, inventoryData] = await Promise.all([
      shipmentModel.getShipmentDetailModelForPdf(shipmentId),
      shipmentModel.getInventoryListForPDF(shipmentId),
    ]);

    myData.isPrintPhotos = isPrintPhotos;
    myData.isDownloadByCustomer = isDownloadByCustomer;

    let sortArray = inventoryData.sort(function (a, b) {
      const nameA = a.qr_id;
      const nameB = b.qr_id;
      if (nameA < nameB) {
        return -1;
      }
      if (nameA > nameB) {
        return 1;
      }
    });

    let newArray = sortArray.sort(function (a, b) {
      const nameA = a.item_qr.type.toUpperCase();
      const nameB = b.item_qr.type.toUpperCase();
      if (nameA > nameB) {
        return -1;
      }
      if (nameA < nameB) {
        return 1;
      }
      return 0;
    });
    let aginNewArray = sortArray.sort(function (a, b) {
      const nameA = a.isManualLabel;
      const nameB = b.isManualLabel;
      if (nameA > nameB) {
        return 1;
      }
      if (nameA < nameB) {
        return -1;
      }
      return 0;
    });

    myData.job_items = [];
    myData.job_items = [...aginNewArray];

    const client = await require("@jsreport/nodejs-client")(
      JSREPORT_URL,
      JSREPORT_ADMIN,
      JSREPORT_PASSWORD
    );
    const res = await client.render({
      template: {
        name: "/assets/Main",
        recipe: "chrome-pdf",
        engine: "handlebars",
      },
      data: {
        data: myData,
      },
    });

    const bodyBuffer = await res.body();

    generateResponse(response, SUCCESS_CODE, 1, "Pdf Downloaded", bodyBuffer);
  } catch (error) {
    console.log("exports.downloadPdfController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.customerPdfController = async (request, response) => {
  try {
    request.connection.setTimeout(1000000000000000);
    const { shipmentId, customer_id, isPrintPhotos, isDownloadByCustomer } =
      request.body;

    const [myData, customerInfo, inventoryData] = await Promise.all([
      shipmentModel.getShipmentDetailModelForPdf(shipmentId),
      shipmentModel.getCustomerInfoforPdf(customer_id),
      shipmentModel.getInventoryListForPDF(shipmentId),
    ]);

    myData.isPrintPhotos =
      isPrintPhotos == 1 ||
        isPrintPhotos == "1" ||
        isPrintPhotos == true ||
        isPrintPhotos == "true"
        ? 1
        : 0;
    myData.isDownloadByCustomer =
      isDownloadByCustomer == 1 ||
        isDownloadByCustomer == "1" ||
        isDownloadByCustomer == true ||
        isDownloadByCustomer == "true"
        ? 1
        : 0;

    let sortArray = inventoryData;
    let newArray = sortArray.sort(function (a, b) {
      const nameA = a.item_qr.type.toUpperCase();
      const nameB = b.item_qr.type.toUpperCase();
      if (nameA > nameB) {
        return -1;
      }
      if (nameA < nameB) {
        return 1;
      }
      return 0;
    });

    let aginNewArray = sortArray.sort(function (a, b) {
      const nameA = a.isManualLabel;
      const nameB = b.isManualLabel;
      if (nameA > nameB) {
        return 1;
      }
      if (nameA < nameB) {
        return -1;
      }
      return 0;
    });

    myData.job_items = [];
    myData.job_items = [...aginNewArray];

    const client = await require("@jsreport/nodejs-client")(
      JSREPORT_URL,
      JSREPORT_ADMIN,
      JSREPORT_PASSWORD
    );
    const res = await client.render({
      template: {
        name: "/assets/Main",
        recipe: "chrome-pdf",
        engine: "handlebars",
      },
      data: {
        data: myData,
      },
    });

    const bodyBuffer = await res.body();
    const pdfBase64 = bodyBuffer.toString("base64");

    let html = await readFile("customerPdf.html");
    html = html.replace(/{{customer_name}}/g, customerInfo.first_name);
    const attachments = [
      {
        filename: "shipmentPdf.pdf",
        content: pdfBase64,
        encoding: "base64",
      },
    ];

    let isEmailSent = await sendEmail(
      customerInfo.email,
      `Mover Inventory - ${myData.shipment_name} Details`,
      html,
      {},
      attachments
    );
    if (isEmailSent === false) {
      throw Error("Unable to send email");
    } else {
      generateResponse(response, SUCCESS_CODE, 1, "PDF send successfully.", {});
    }
  } catch (error) {
    console.log("exports.downloadPdfController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.downloadPicklistPdfController = async (request, response) => {
  try {
    request.connection.setTimeout(1000000000000000);
    const { shipmentId } = request.body;
    const myData = await shipmentModel.getShipmentDetailModelForPicklistPdf(
      shipmentId
    );

    let sortArray = await myData.job_units;
    sortArray.sort(function (a, b) {
      const nameA = a.name;
      const nameB = b.name;
      if (nameA > nameB) {
        return 1;
      }
      if (nameA < nameB) {
        return -1;
      }
      return 0;
    });

    const client = await require("@jsreport/nodejs-client")(
      JSREPORT_URL,
      JSREPORT_ADMIN,
      JSREPORT_PASSWORD
    );
    const res = await client.render({
      template: {
        name: "/picklistPdf/Main",
        recipe: "chrome-pdf",
        engine: "handlebars",
      },
      data: {
        data: myData,
      },
    });

    const bodyBuffer = await res.body();

    generateResponse(response, SUCCESS_CODE, 1, "Pdf Downloaded", bodyBuffer);
  } catch (error) {
    console.log("exports.downloadPicklistPdfController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.scriptShipmentController = async (request, response) => {
  try {
    const { qty } = request.body;

    for (let i = 0; i < qty; i++) {
      let jobNumber = randomSequencedString(6);
      let createShipmentScript = await shipmentModel.createShipmentScript(
        request.body,
        jobNumber,
        i
      );
    }

    generateResponse(response, SUCCESS_CODE, 1, SHIPMENT_ADD_SUCCESS, {});
  } catch (error) {
    console.log("exports.scriptShipmentController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.isStorageShipmentExitsController = async (request, response, next) => {
  try {
    const shipmentData = await shipmentModel.isStorageShipmentExitsModel(
      request.body
    );
    if (!shipmentData) {
      next();
    } else
      generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SHIPMENT_ALREADY_EXIST,
        shipmentData
      );
  } catch (error) {
    console.log("exports.isStorageShipmentExitsController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.isPickupDateMandatoryCheck = async (request, response, next) => {
  try {
    const fetchShipmentTypeForShipment =
      await shipmentModel.fetchShipmentTypeForShipment(request.body);
    if (fetchShipmentTypeForShipment.is_pickup_date_mandatory == 1) {
      if (
        request.body.pickup_date !== null &&
        request.body.pickup_date !== undefined &&
        request.body.pickup_date !== ""
      ) {
        next();
      } else {
        generateResponse(
          response,
          NOT_VALID_DATA_CODE,
          0,
          "Please select pickup date. Pickup date cannot be blank.",
          {}
        );
      }
    } else {
      next();
    }
  } catch (error) {
    console.log("exports.isPickupDateMandatoryCheck -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.createShipmentController = async (request, response) => {
  try {
    let getUserDetails = await commonModel.getUserDetails(request);
    let params = {
      job_number: randomSequencedString(6),
      created_by_type: getUserDetails.company_id ? "company" : "staff",
      created_by_id: getUserDetails.company_id
        ? getUserDetails.company_id
        : getUserDetails.staff_id,
      ...request.body,
    };
    let tagShipment = [];
    const shipmentData = await shipmentModel.addShipmentModel(params);
    //newChanges
    await shipmentModel.updateCustomerCountModel(shipmentData.customer_id);
    if (params.tag && params.tag.length > 0) {
      params.tag.map((tag) =>
        tagShipment.push({
          shipment_id: shipmentData.shipment_job_id,
          tag_id: tag,
        })
      );
      await shipmentModel.addUpdateTagToShipmentModel(
        tagShipment,
        shipmentData.shipment_job_id
      );
    }

    const fetchShipmentTypeForShipment =
      await shipmentModel.fetchShipmentTypeForShipment(request.body);
    const createShipmentTypeForShipment =
      await shipmentModel.createShipmentTypeForShipment(
        fetchShipmentTypeForShipment,
        shipmentData
      );
    const upShipment = await shipmentModel.upShipment(
      createShipmentTypeForShipment,
      shipmentData.shipment_job_id
    );

    let fetchShipmentTypeStagesForShipment =
      await shipmentTypeModel.fetchShipmentTypeStagesForShipment(request.body);
    let scanIntoStorageCheck = false;
    fetchShipmentTypeStagesForShipment.forEach((newData) => {
      if (newData.assign_storage_units_to_items === 1) {
        scanIntoStorageCheck = true;
      }
    });
    let createShipmentTypeStagesForShipment =
      await shipmentTypeModel.createShipmentTypeStagesForShipment(
        fetchShipmentTypeStagesForShipment,
        createShipmentTypeForShipment
      );
    const upShipmentStageUpdate = await shipmentModel.upShipmentStageUpdate(
      createShipmentTypeStagesForShipment,
      shipmentData.shipment_job_id
    );
    const updatedDetails = { ...shipmentData.toJSON(), scanIntoStorageCheck };
    ActionLogModel.createActionLog({
      platform: "CMS",
      performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
      performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
      performed_by_name: getUserDetails.name,
      action_type: "SHIPMENT_CREATE",
      action_performed_on_id: shipmentData.shipment_job_id,
      action_performed_on_name: shipmentData.shipment_name,
    })
    generateResponse(
      response,
      SUCCESS_CODE,
      1,
      SHIPMENT_ADD_SUCCESS,
      updatedDetails
    );
  } catch (error) {
    console.log("exports.createShipmentController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.getShipmentDetailController = async (request, response) => {
  try {
    const { shipmentId } = request.params;
    const { orderingField, orderingWay } = request.query;
    const getShipmentAssignWorkerData =
      await shipmentModel.getShipmentAssignWorkerData(shipmentId);
    const getShipmentModelData = await shipmentModel.getShipmentDetailModel(
      request.company_id,
      shipmentId,
      orderingField,
      orderingWay
    );
    getShipmentModelData.assign_worker = getShipmentAssignWorkerData;
    if (getShipmentModelData)
      generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SHIPMENT_RETRIEVED_SUCCESS,
        getShipmentModelData
      );
    else generateResponse(response, SERVER_ERROR_CODE, 0, SHIPMENT_EMPTY, {});
  } catch (error) {
    console.log("exports.getShipmentDetailController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.getShipmentDetailForCustomerController = async (request, response) => {
  try {
    const { shipmentId } = request.params;
    const { orderingField } = request.query;
    const getShipmentModelData =
      await shipmentModel.getShipmentDetailForCustomerModel(
        shipmentId,
        orderingField
      );
    if (getShipmentModelData)
      generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SHIPMENT_RETRIEVED_SUCCESS,
        getShipmentModelData
      );
    else generateResponse(response, SUCCESS_CODE, 0, SHIPMENT_EMPTY, {});
  } catch (error) {
    console.log(
      "exports.getShipmentDetailForCustomerController -> error: ",
      error
    );
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.listShipmentController = async (request, response) => {
  try {
    request.query.search = request.query.search ? request.query.search : "";
    const listShipmentData = await shipmentModel.listShipmentModel(
      request.company_id,
      request.query
    );
    if (listShipmentData)
      generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SHIPMENT_RETRIEVED_SUCCESS,
        listShipmentData
      );
    else generateResponse(response, NOT_FOUND_CODE, 0, SHIPMENT_LIST_EMPTY, {});
  } catch (error) {
    console.log("exports.listShipmentController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.basicShipmentTypeController = async (request, response) => {
  try {
    const listShipmentData = await shipmentModel.basicShipmentStagesModel(
      request.query
    );

    if (listShipmentData)
      generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SHIPMENT_STAGE_RETRIEVED_SUCCESS,
        listShipmentData
      );
    else
      generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        SHIPMENT_STAGE_NOT_FOUND,
        {}
      );
  } catch (error) {
    console.log("exports.basicShipmentTypeController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.basicShipmentController = async (request, response) => {
  try {
    const basicListShipmentData = await shipmentModel.basicShipmentModel();
    if (basicListShipmentData)
      generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SHIPMENT_RETRIEVED_SUCCESS,
        basicListShipmentData
      );
    else generateResponse(response, NOT_FOUND_CODE, 0, SHIPMENT_LIST_EMPTY, {});
  } catch (error) {
    console.log("exports.basicShipmentController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.consumerLoginJsonFun = async (request, response, integration_key) => {
  const consumerLoginJson = JSON.stringify({
    companyIdTokenMoverInventory: integration_key,
    email: "<EMAIL>",
    password: "5PLaRAqq",
    deviceToken: "abcd",
    deviceType: 0,
  });
  try {
    const consumerLoginResponse = await axios.post(
      `${MOVER_STORAGE_API_URL}login`,
      consumerLoginJson,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    if (
      consumerLoginResponse.data !== "" &&
      consumerLoginResponse.data !== undefined &&
      consumerLoginJson.data !== null
    ) {
      return consumerLoginResponse.data;
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "Consumer Login Fail",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Consumer Login Fail",
      {}
    );
  }
};

exports.shipmentDeleteJson = async (
  request,
  response,
  fetchShipmentForStorage,
  consumerLoginJson
) => {
  const shipmentId = fetchShipmentForStorage.storage_shipment_job_id;
  const shipmentDeleteJson = JSON.stringify({
    deleted: true,
    isActive: 0,
  });
  try {
    const shipmentDeleteResponse = await axios.put(
      `${MOVER_STORAGE_API_URL}import/mover-inventory/shipments/active-status/${shipmentId}`,
      shipmentDeleteJson,
      {
        headers: {
          "Content-Type": "application/json",
          accessToken: consumerLoginJson.data.accessToken,
        },
      }
    );
    if (shipmentDeleteResponse.status == 200) {
      return shipmentDeleteResponse;
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "Shipment delete fail",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Shipment delete fail",
      {}
    );
  }
};

exports.isItemsAssignToShipmentTypeStageController = async (
  request,
  response,
  next
) => {
  try {
    const { stageId } = request.params;
    const resData = await shipmentModel.isItemsAssignToShipmentTypeStageModel(
      stageId
    );
    if (resData.count > 0) {
      generateResponse(
        response,
        EXPECTATION_FAILED_CODE,
        0,
        "Items assign to the shipment type stage. we can not delete shipment type stage, please try again!.",
        {}
      );
    } else {
      next();
    }
  } catch (error) {
    console.log(
      "exports.isItemsAssignToShipmentTypeStageController -> error: ",
      error
    );
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.removeShipmentTypeStageController = async (request, response) => {
  try {
    const { stageId } = request.params;
    const removedShipmentTypeStage =
      await shipmentModel.deleteShipmentTypeStageModel(stageId);
    generateResponse(
      response,
      SUCCESS_CODE,
      1,
      "Shipment type stage deleted successfully",
      {}
    );
  } catch (error) {
    console.log("exports.removeShipmentTypeStageController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.removeShipmentController = async (request, response) => {
  try {
    const { shipmentId } = request.params;
    const { model_customer_id } = request.body;
    let getUserDetails = await commonModel.getUserDetails(request);
    const viewShipment = await shipmentModel.fetchShipmentWarehouse(
      shipmentId
    );

    const fetchShipmentForStorage =
      await shipmentModel.fetchShipmentForStorageDelete(shipmentId);
    const integration_key = fetchShipmentForStorage.dataValues.integration_key
      ? fetchShipmentForStorage.dataValues.integration_key
      : fetchShipmentForStorage.integration_key;
    if (
      fetchShipmentForStorage.storage_shipment_job_id !== undefined &&
      fetchShipmentForStorage.storage_shipment_job_id !== null &&
      fetchShipmentForStorage.storage_shipment_job_id !== ""
    ) {
      const consumerLoginJson = await this.consumerLoginJsonFun(
        request,
        response,
        integration_key
      );
      const shipmentDeleteJson = await this.shipmentDeleteJson(
        request,
        response,
        fetchShipmentForStorage,
        consumerLoginJson
      );
    }
    const deleteShipmentItems = await shipmentModel.deleteShipmentItems(
      shipmentId
    );
    const removedShipment = await shipmentModel.deleteShipmentModel(shipmentId);
    const updateCustomerCountModel =
      await shipmentModel.updateCustomerCountModel(model_customer_id);

      ActionLogModel.createActionLog({
        platform: "CMS",
        performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
        performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
        performed_by_name: getUserDetails.name,
        action_type: "SHIPMENT_DELETE",
        action_performed_on_id: shipmentId,
        action_performed_on_name: viewShipment.shipment_name,
      })

    generateResponse(
      response,
      SUCCESS_CODE,
      1,
      SHIPMENT_DELETED_SUCCESS,
      removedShipment
    );
  } catch (error) {
    console.log("exports.removeShipmentController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.updateWarehouseController = async (request, response, next) => {
  try {
    const { shipmentId } = request.params;
    const fetchShipmentWarehouse = await shipmentModel.fetchShipmentWarehouse(
      shipmentId
    );
    if (
      fetchShipmentWarehouse.warehouseId !== null &&
      fetchShipmentWarehouse.warehouseId !== "" &&
      fetchShipmentWarehouse.warehouseId !== undefined
    ) {
      if (fetchShipmentWarehouse.warehouseId == request.body.warehouseId) {
        next();
      } else {
        const fetchShipmentItemCount =
          await shipmentModel.fetchShipmentItemCount(
            shipmentId,
            fetchShipmentWarehouse.warehouseId
          );
        if (fetchShipmentItemCount.count > 0) {
          generateResponse(
            response,
            SERVER_ERROR_CODE,
            0,
            "Items assign to the units. we can not update warehouse, please try again!.",
            {}
          );
        } else {
          next();
        }
      }
    } else {
      next();
    }
  } catch (error) {
    console.log("exports.updateWarehouseController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.updateShipmentController = async (request, response) => {
  try {
    const { shipmentId } = request.params;
    const viewShipment = await shipmentModel.fetchShipmentWarehouse(
      shipmentId
    );
    let getUserDetails = await commonModel.getUserDetails(request);
    const requestBody = request.body;
    let tagShipment = [];
    //newChanges
    const removedShipment = await shipmentModel.updateShipmentModel(
      shipmentId,
      requestBody
    );
    if (removedShipment[0] === 1) {
      if (requestBody.tag && requestBody.tag.length > 0) {
        requestBody.tag.map((tag) =>
          tagShipment.push({
            shipment_id: shipmentId,
            tag_id: tag,
          })
        );
        shipmentModel.addUpdateTagToShipmentModel(tagShipment, shipmentId);
      }
      ActionLogModel.createActionLog({
        platform: "CMS",
        performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
        performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
        performed_by_name: getUserDetails.name,
        action_type: "SHIPMENT_UPDATE",
        action_performed_on_id: shipmentId,
        action_performed_on_name: viewShipment.shipment_name,
      })
      generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SHIPMENT_UPDATED_SUCCESS,
        removedShipment
      );
    } else
      generateResponse(
        response,
        SUCCESS_CODE,
        0,
        SHIPMENT_NOT_UPDATED_SUCCESS,
        {}
      );
  } catch (error) {
    console.log("exports.updateShipmentController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.assignWorkerShipmentController = async (request, response) => {
  try {
    const { role, staff_id } = request.body;
    const { shipmentId } = request.params;
    const modelResponse = await shipmentModel.assignShipmentJobWorker(
      shipmentId,
      role,
      staff_id
    );
    generateResponse(
      response,
      SUCCESS_CODE,
      1,
      SHIPMENT_UPDATED_SUCCESS,
      modelResponse
    );
  } catch (error) {
    console.log("exports.assignWorkerShipmentController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.unlinkWorkerShipmentController = async (request, response) => {
  try {
    const { staff_worker_id, role } = request.body;
    const { shipmentId } = request.params;

    const modelResponse = await shipmentModel.unlinkShipmentJobWorker(
      shipmentId,
      staff_worker_id,
      role
    );
    generateResponse(
      response,
      SUCCESS_CODE,
      1,
      SHIPMENT_UPDATED_SUCCESS,
      modelResponse
    );
  } catch (error) {
    console.log("exports.unlinkWorkerShipmentController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.forceShipmentStageController = async (request, response) => {
  try {
    const { altered_stage_id, reason } = request.body;
    const { shipmentId } = request.params;
    if (altered_stage_id === -1) {
      const modelResponse = await shipmentModel.modelResponseShipmentComplete(
        shipmentId
      );
      generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SHIPMENT_UPDATED_SUCCESS,
        modelResponse
      );
    } else {
      const modelResponse = await shipmentModel.forceUpdateWorFlowModel(
        shipmentId,
        reason,
        altered_stage_id
      );
      generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SHIPMENT_UPDATED_SUCCESS,
        modelResponse
      );
    }
  } catch (error) {
    console.log("exports.forceShipmentStageController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.fetchShipmentForStorageController = async (request, response, next) => {
  try {
    const fetchShipmentForStorage = await shipmentModel.fetchShipmentForStorage(
      request.params
    );
    if (fetchShipmentForStorage) {
      generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SHIPMENT_RETRIEVED_SUCCESS,
        fetchShipmentForStorage
      );
    } else {
      generateResponse(response, NOT_FOUND_CODE, 0, SHIPMENT_EMPTY, {});
    }
  } catch (error) {
    console.log("exports.fetchShipmentForStorageController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.staffAssignToShipmentStage = async (request, response) => {
  try {
    const { role, staff_id, stage_id } = request.body;
    const { shipmentId } = request.params;

    let modelResponse;

    const isStaffAlreadyAssigned =
      await shipmentModel.isStaffAlreadyAssignToStage(
        shipmentId,
        staff_id,
        stage_id
      );
    if (isStaffAlreadyAssigned && isStaffAlreadyAssigned.role === role) {
      return generateResponse(
        response,
        EXPECTATION_FAILED_CODE,
        0,
        "Worker already assigned to stage. Please try with another",
        {}
      );
    }

    if (role === "supervisor") {
      const supervisorAssignment =
        await shipmentModel.isSupervisorAssignToShipmentStage(
          shipmentId,
          stage_id,
          role
        );
      if (supervisorAssignment) {
        await shipmentModel.deleteExitingWorkerFromStage(supervisorAssignment);
      }
    }

    if (isStaffAlreadyAssigned) {
      await shipmentModel.deleteExitingWorkerFromStage(isStaffAlreadyAssigned);
    }


    modelResponse = await shipmentModel.assignNewStaffToShipment(
      shipmentId,
      role,
      staff_id,
      stage_id
    );
    console.log("🚀 ~ modelResponse:", modelResponse)

    ActionLogModel.createActionLog({
      platform: "CMS",
      staff_id: staff_id,
      shipment_job_id: shipmentId,
      local_shipment_stage_id: stage_id,
      assign_job_worker_id: modelResponse.assign_job_worker_id,
      action_type: "STAGE_ASSIGN",
    });

    generateResponse(
      response,
      SUCCESS_CODE,
      1,
      SHIPMENT_UPDATED_SUCCESS,
      modelResponse
    );
  } catch (error) {
    console.log("exports.staffAssignToShipmentStage -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.isValidShipmentController = async (request, response, next) => {
  try {
    const shipmentId = request.params.shipmentId
      ? request.params.shipmentId
      : request.body.shipment_id;
    const getShipmentModelInstance = await shipmentModel.isValidShipmentIdModel(
      shipmentId
    );
    if (getShipmentModelInstance) {
      request.body.model_customer_id = getShipmentModelInstance.customer_id;
      request.body.model_customer_email = getShipmentModelInstance.email;
      next();
    } else generateResponse(response, NOT_FOUND_CODE, 0, SHIPMENT_EMPTY, {});
  } catch (error) {
    console.log("exports.isValidShipmentController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.isValidShipmentStageIdController = async (request, response, next) => {
  try {
    const { current_job_stage, shipment_id } = request.body;
    const findShipmentCurrentStage =
      await shipmentModel.findShipmentCurrentStage(shipment_id);
    if (findShipmentCurrentStage.local_job_status == current_job_stage) {
      next();
    } else {
      generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        "Please enter valid shipment stage Id",
        {}
      );
    }
  } catch (error) {
    console.log("exports.isValidShipmentController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.isActiveCustomerForShipmentController = async (
  request,
  response,
  next
) => {
  try {
    const { customer_id } = request.body;
    const shipmentId = request.params.shipmentId
      ? request.params.shipmentId
      : request.body.shipment_id;
    const data = await shipmentModel.findCustomerStatusById(customer_id);
    const jobData = await shipmentModel.findShipmentStatusById(shipmentId);
    if (
      jobData.is_job_complete_flag == 1 ||
      jobData.dataValues.is_first_stage == "no"
    ) {
      next();
    } else if (data.status == "active") {
      next();
    } else
      generateResponse(
        response,
        BAD_REQUEST_CODE,
        0,
        INACTIVE_CUSTOMER_UPDATE_FOR_SHIPMENT,
        {}
      );
  } catch (error) {
    console.log(
      "exports.isActiveCustomerForShipmentController -> error: ",
      error
    );
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

// after stage add to add items
exports.isValidShipmentItemAndWorkerController = async (
  request,
  response,
  next
) => {
  try {
    const shipmentId = (await request.params.shipmentId)
      ? request.params.shipmentId
      : request.body.shipment_id;
    const forceStageChangeId =
      await shipmentModel.getShipmentStageIdForForceStageChange(shipmentId);
    const checkshipmentsValidItems =
      await shipmentModel.checkshipmentsValidItems(
        shipmentId,
        forceStageChangeId
      );
    if (checkshipmentsValidItems.count > 0) {
      generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        "Can not change the shipment stage. Shipment have items ",
        {}
      );
    } else {
      next();
    }
  } catch (error) {
    console.log(
      "exports.isValidShipmentItemAndWorkerController -> error: ",
      error
    );
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.isCheckValidShipmentController = async (request, response, next) => {
  try {
    const shipmentId = request.params.shipmentId
      ? request.params.shipmentId
      : request.body.shipment_id;

    const getShipmentModelInstance = await shipmentModel.isValidShipmentIdModel(
      shipmentId
    );
    if (getShipmentModelInstance) {
      next();
    } else generateResponse(response, NOT_FOUND_CODE, 0, SHIPMENT_EMPTY, {});
  } catch (error) {
    console.log("exports.isCheckValidShipmentController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.isCheckValidShipmentForCmsController = async (
  request,
  response,
  next
) => {
  try {
    const shipmentId = request.body.shipmentId;
    const getShipmentModelInstance = await shipmentModel.isValidShipmentIdModel(
      shipmentId
    );
    if (getShipmentModelInstance) {
      next();
    } else generateResponse(response, NOT_FOUND_CODE, 0, SHIPMENT_EMPTY, {});
  } catch (error) {
    console.log(
      "exports.isCheckValidShipmentForCmsController -> error: ",
      error
    );
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.isCheckValidShipmentforItemsController = async (
  request,
  response,
  next
) => {
  try {
    const shipmentId = request.body.shipmentId;
    const getShipmentModelInstance = await shipmentModel.isValidShipmentIdModel(
      shipmentId
    );
    if (getShipmentModelInstance) {
      next();
    } else generateResponse(response, NOT_FOUND_CODE, 0, SHIPMENT_EMPTY, {});
  } catch (error) {
    console.log(
      "exports.isCheckValidShipmentforItemsController -> error: ",
      error
    );
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.changeShipmentStageController = async (request, response, next) => {
  try {
    const { job_id, stage } = request.body;
    const shipmentInstance = await shipmentModel.changeShipmentStage(
      job_id,
      stage
    );
    if (shipmentInstance) {
      next();
    } else {
      generateResponse(response, SERVER_ERROR_CODE, 0, ADD_SIGNATURE_FAIL, {});
    }
  } catch (error) {
    console.log("exports.changeShipmentStageController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.printJobSummaryController = async (request, response) => {
  try {
    const { shipmentId } = request.params;
    const shipmentInstance = await shipmentModel.getShipmentDetailModel(
      null,
      shipmentId
    );

    if (shipmentInstance) {
      let baseFolderPath = `/tmp/summaryPdf/${shipmentInstance.shipment_job_id}`;
      if (!fs.existsSync(baseFolderPath))
        fs.mkdirSync(`${baseFolderPath}`, { recursive: true });
      const finalPdfPath = `${baseFolderPath}/${shipmentInstance.job_number
        }_${moment().toISOString()}.pdf`;

      //newChanges
      const r = new PDFGeneration(shipmentInstance, finalPdfPath).createPDF();
      if (r) {
        r.on("finish", () => {
          fs.readFile(finalPdfPath, (err, fileData) => {
            if (!err) {
              response.status(CREATED_CODE);
              response.contentType("application/pdf");
              response.send(fileData);
            } else {
              generateResponse(
                response,
                SERVER_ERROR_CODE,
                0,
                SHIPMENT_PRINT_FAILED,
                {}
              );
            }
          });
        });
      }
    } else
      generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        SHIPMENT_PRINT_FAILED,
        {}
      );
  } catch (error) {
    console.log("exports.printJobSummaryController -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.checkShipmentAssignToJob = async (request, response, next) => {
  try {
    const { shipmentId } = request.params;
    const fetchShipmentWarehouse = await shipmentModel.fetchShipmentWarehouse(
      shipmentId
    );
    if (
      fetchShipmentWarehouse.warehouseId !== null &&
      fetchShipmentWarehouse.warehouseId !== "" &&
      fetchShipmentWarehouse.warehouseId !== undefined
    ) {
      const fetchShipmentItemCount = await shipmentModel.fetchShipmentItemCount(
        shipmentId,
        fetchShipmentWarehouse.warehouseId
      );
      if (fetchShipmentItemCount.count > 0) {
        generateResponse(
          response,
          EXPECTATION_FAILED_CODE,
          0,
          SHIPMENT_INVENTORY_ASSIGN_TO_UNITS,
          {}
        );
      } else {
        next();
      }
    } else {
      next();
    }
  } catch (error) {
    console.log("exports.checkShipmentAssignToJob -> error: ", error);
    generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};
